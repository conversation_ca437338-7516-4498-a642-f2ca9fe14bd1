package com.wonderslate.data

import com.wonderslate.cache.DataProviderService
import com.wonderslate.log.GptDefaultCreateLog
import com.wonderslate.log.AutogptLog
import com.wonderslate.logs.AutogptErrorLoggerService
import com.wonderslate.sqlutil.SafeSql
import grails.converters.JSON
import grails.transaction.Transactional
import groovy.json.JsonBuilder
import groovy.json.JsonSlurper
import org.grails.web.json.JSONObject
import org.springframework.transaction.annotation.Propagation


class AutogptService {

    DataProviderService dataProviderService
    def grailsApplication
    PromptService promptService
    AutogptErrorLoggerService autogptErrorLoggerService

    def getChapterMetaData(params){
         ResourceDtl resourceDtl = ResourceDtl.findById(new Long(params.resId))
        ChaptersMst chaptersMst = ChaptersMst.findById(resourceDtl.chapterId)
        if (!resourceDtl) {
            def json = [status: "ERROR", message: "Resource not found for resId: " + params.resId]
            return json
        }

        String extractPath = resourceDtl.extractPath
        if (!extractPath || extractPath.trim().isEmpty()) {
            extractPath = "supload/pdfextracts/"+chaptersMst.bookId+"/"+chaptersMst.id+"/"+resourceDtl.id+"/extractedImages/"+resourceDtl.id+".txt"
        }
        //check if the sourceFile exists
        File sourceFile = new File(grailsApplication.config.grails.basedir.path + "/" + extractPath)
        if(!sourceFile.exists()){
            //create the vector file it is not present
            storePdfVectors(params)
        }
        String folderPath = extractPath.substring(0, extractPath.lastIndexOf("/"))
         File metadataFile = new File(grailsApplication.config.grails.basedir.path + "/"+folderPath+"/chapterMetadata"+ chaptersMst.id + ".txt")
         if(metadataFile.exists()){
            metadataFile.delete()
        }

            // Construct full file path
            String filePath = grailsApplication.config.grails.basedir.path + "/" + extractPath
            File textFile = new File(filePath)
            //get the full content of the file to a string
            String fileContent = textFile.text

            Prompts prompts = Prompts.findByPromptType("chapterMetadataExtractor")
            String response = getLLMResponse(resourceDtl, fileContent, prompts.basePrompt,params.serverIPAddress)
            metadataFile.write(response)


        def json = [status:"OK"]
        return json
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def exerciseCollector(params) {
        int noOfQuestionsPerIteration = 5
        String allResponse = ""
        int totalQuestions = 0

        try {
            // Check if resId parameter is provided
            if (!params.resId) {
                def json = [status: "ERROR", message: "resId parameter is required"]
                return json
            }

            // Get ResourceDtl record using resId
            ResourceDtl resourceDtl = ResourceDtl.findById(new Long(params.resId))
            ChaptersMst chaptersMst = ChaptersMst.findById(resourceDtl.chapterId)
            //check if there is instance of resourceDtl for given chapterId and resourceName starting with "Exercise"
            boolean exerciseCreated = false
            ResourceDtl exerciseResourceDtl = ResourceDtl.findByChapterIdAndResourceName(chaptersMst.id,"Exercise Solutions")
            if(exerciseResourceDtl!=null) {
                try {
                    ObjectiveMst objectiveMst = ObjectiveMst.findByQuizId(new Integer(exerciseResourceDtl.resLink))
                    if (objectiveMst != null) exerciseCreated = true
                 }catch (Exception e){
                    exerciseCreated = false
                }
            }
            if(!exerciseCreated) {
                BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
                if (!resourceDtl) {
                    def json = [status: "ERROR", message: "Resource not found for resId: " + params.resId]
                    return json
                }

                String extractPath = resourceDtl.extractPath
                if (!extractPath || extractPath.trim().isEmpty()) {
                    extractPath = "supload/pdfextracts/"+chaptersMst.bookId+"/"+chaptersMst.id+"/"+resourceDtl.id+"/extractedImages/"+resourceDtl.id+".txt"
                }
                File sourceFile = new File(grailsApplication.config.grails.basedir.path + "/" + extractPath)
                if(!sourceFile.exists()){
                    //create the vector file it is not present
                    storePdfVectors(params)
                }

                // Construct full file path
                String filePath = grailsApplication.config.grails.basedir.path + "/" + extractPath

                File textFile = new File(filePath)

                if (!textFile.exists()) {
                    def json = [status: "ERROR", message: "Text file not found at path: " + filePath]

                    return json
                }

                // Read file content in chunks of 20000 characters
                def chunkSize = 20000
                def chunkNumber = 1

                ResourceDtl resourceDtlInstance = null
                if(exerciseResourceDtl!=null) resourceDtlInstance = exerciseResourceDtl
                textFile.withReader('UTF-8') { reader ->
                    char[] buffer = new char[chunkSize]
                    StringBuilder currentChunk = new StringBuilder()
                    int charsRead

                    while ((charsRead = reader.read(buffer, 0, chunkSize)) != -1) {
                        currentChunk.append(buffer, 0, charsRead)

                        // If we have read exactly chunkSize characters, look for next paragraph break
                        if (charsRead == chunkSize) {
                            // Read additional characters until we find a paragraph break
                            int nextChar
                            while ((nextChar = reader.read()) != -1) {
                                currentChunk.append((char) nextChar)

                                // Check for paragraph break (double newline)
                                String content = currentChunk.toString()
                                if (content.endsWith("\n\n") || content.endsWith("\r\n\r\n")) {
                                    break
                                }
                            }
                        }


                        def response = examplesAndExercisesExtractor(resourceDtl, currentChunk.toString(),params.serverIPAddress)

                        String question, answer
                        //sometimes response is "[]\n[]\n" we have check for that also
                        if (response != null && response.size() > 0 && !"[]\n[]\n".equals(response)) {
                            //for each response get the question element and print it
                            if (resourceDtlInstance == null) {
                                QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
                                quizIdGenerator.save()

                                resourceDtlInstance = new ResourceDtl()
                                resourceDtlInstance.resLink = quizIdGenerator.id
                                resourceDtlInstance.createdBy = "System"
                                resourceDtlInstance.resType = "QA"
                                resourceDtlInstance.chapterId = resourceDtl.chapterId
                                resourceDtlInstance.resourceName = "Exercise Solutions"
                                resourceDtlInstance.save(failOnError: true, flush: true)


                            }
                            try {
                                int numberOfQuestions = response.size()
                                totalQuestions += numberOfQuestions
                                int currentQuestionIndex = 0
                                while(currentQuestionIndex<numberOfQuestions){
                                    String inputQuestions = "The questions are \n"

                                    int noOfQuestionsForLoop = noOfQuestionsPerIteration
                                    if(numberOfQuestions-currentQuestionIndex<noOfQuestionsPerIteration){
                                        noOfQuestionsForLoop = numberOfQuestions-currentQuestionIndex
                                    }
                                    for(int i=currentQuestionIndex;i<currentQuestionIndex+noOfQuestionsForLoop;i++){
                                        inputQuestions += (i+1)+". "+response[i].question+"\n"
                                    }

                                    //clean up the inputQuestions
                                      inputQuestions = jsonCleaner(inputQuestions)
                                     def jsonAnswer = getSolution(resourceDtl, inputQuestions, chaptersMst.id, booksMst.id,"solutionCreator",1,params.serverIPAddress)
                                     def jsonAnswerList
                                    try {
                                        if (jsonAnswer != null) {
                                            try {
                                                jsonAnswerList = new JsonSlurper().parseText(jsonCleaner(jsonAnswer.answer))
                                            }catch (Exception e){
                                                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "solutionCreator", "Exception while parsing jsonAnswer"+e.toString(), jsonAnswer.answer)
                                                jsonAnswer.answer = fixJSONFromLLM(jsonAnswer.answer)
                                                jsonAnswerList = new JsonSlurper().parseText(jsonCleaner(jsonAnswer.answer))
                                            }
                                            jsonAnswerList.each { json ->
                                                answer = json.solution
                                                //add the question and answer to ObjectiveMst
                                                ObjectiveMst om = new ObjectiveMst(quizId: new Integer(resourceDtlInstance.resLink), quizType: "QA", question:  fixFormulas(json.question),
                                                        answer: fixFormulas(json.solution),
                                                        difficultylevel: json.difficultyLevel, qType: json.questionType, answerDescription: fixFormulas(json.explanation), bloomType: json.bloomLevel)
                                                om.save(failOnError: true, flush: true)

                                                if(booksMst!=null&&!"English".equals(booksMst.language)&&!"".equals(booksMst.language)&&booksMst.language!=null){
                                                    fixLanguage(""+om.id,"subjective",booksMst.language)
                                                }
                                                if("true".equals(""+json.hasFormula)){
                                                    fixQuestion(""+om.id, "subjective")
                                                }
                                            }

                                        }
                                    }catch (Exception e){
                                      autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "solutionCreatorFixed", "Exception while parsing jsonAnswer "+e.toString(), jsonCleaner(jsonAnswer.answer))

                                    }
                                    currentQuestionIndex += noOfQuestionsForLoop
                                }

                            } catch (Exception e) {
                                println("Exception in exerciseCollector: " + e.toString())
                            }
                        }

                        chunkNumber++
                        currentChunk.setLength(0) // Clear the buffer for next chunk
                    }
                }

                dataProviderService.getChaptersList(chaptersMst.bookId);
                def json = [status: "OK", message: "File processed successfully"]
                return json
            }else{
                def json = [status: "OK", message: "Exercises already created"]
                return json
            }
        } catch (Exception e) {
            println("Exception in exerciseCollector1: " + e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
            def json = [status: "ERROR", message: "Error processing file: " + e.message]
            return json
        }
    }

    def examplesAndExercisesExtractor(ResourceDtl resourceDtl,String inputText,String serverIPAddress){
        List responseList =[]
        try {
            Prompts prompts = Prompts.findByPromptType("exerciseExtractor")
            String responseAnswer = getLLMResponse(resourceDtl,inputText,prompts.basePrompt,serverIPAddress)
            if(responseAnswer.startsWith("Error-")){
                String errorMessage = responseAnswer.substring(6)
                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "exerciseExtractor", errorMessage, inputText)
                inputText = fixJSONFromLLM(inputText)
                try {
                    responseAnswer = getLLMResponse(resourceDtl,inputText,prompts.basePrompt,serverIPAddress)
                }catch (Exception e){
                    autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "exerciseExtractorFixed", "Exception in getLLMResponse", inputText)
                    return responseList
                }
            }

            try {
                responseList = new JsonSlurper().parseText(responseAnswer)
            }catch (Exception e){
                println("Exception in examplesAndExercisesExtractor "+e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "exerciseExtractor", "Exception in parsing", responseAnswer)
                responseAnswer = fixJSONFromLLM(responseAnswer)
                try {
                    responseList = new JsonSlurper().parseText(responseAnswer)
                }catch (Exception e1){
                    autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "exerciseExtractorFixed", "Exception in parsing", responseAnswer)
                }
                return responseList
            }
            return responseList
        }catch (Exception e){
            return "Exception happened: "+e.getMessage()
        }
    }

    def getLLMResponse(ResourceDtl resourceDtl,String inputText,String basePrompt,String serverIPAddress = null){
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
        String extractPath = resourceDtl.extractPath
        if (!extractPath || extractPath.trim().isEmpty()) {
            extractPath = "supload/pdfextracts/"+chaptersMst.bookId+"/"+chaptersMst.id+"/"+resourceDtl.id+"/extractedImages/"+resourceDtl.id+".txt"
        }
        String folderPath = extractPath.substring(0, extractPath.lastIndexOf("/"))
        File file = new File(grailsApplication.config.grails.basedir.path + "/"+folderPath+"/extractData"+ resourceDtl.id + ".txt")
        try {
            if(file.exists()) {
                file.delete()
            }
            file = new File(grailsApplication.config.grails.basedir.path + "/"+folderPath+"/extractData"+ resourceDtl.id + ".txt")
            file.append(inputText);
            URL url = new URL("http://"+serverIPAddress+":8000/api" + "/retrieveDataAdminText")
            HttpURLConnection conn = (HttpURLConnection) url.openConnection()
            conn.setRequestMethod("POST")

            String boundary = "===" + System.currentTimeMillis() + "==="
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary)
            conn.setDoOutput(true)

            String prompt = basePrompt
            prompt = fixCurlyBrackets(prompt)
            try {
                OutputStream output = conn.getOutputStream();
                PrintWriter writer = new PrintWriter(new OutputStreamWriter(output, "UTF-8"), true)
                writer.append("\r\n").flush()
                writer.append("--" + boundary).append("\r\n")
                writer.append("Content-Disposition: form-data; name=\"prompt\"").append("\r\n")
                writer.append("Content-Type: text/plain; charset=UTF-8").append("\r\n")
                writer.append("\r\n").flush()
                output.write(prompt.getBytes("UTF-8"))
                output.flush()

                // Send file.
                writer.append("\r\n").flush()
                writer.append("--" + boundary).append("\r\n")
                writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"file.txt\"").append("\r\n")
                writer.append("Content-Type: text/plain; charset=UTF-8").append("\r\n")
                writer.append("\r\n").flush()
                output.write(file.text.getBytes("UTF-8"))
                output.flush()

                // End of multipart/form-data.
                writer.append("\r\n").flush()
                writer.append("--" + boundary + "--").append("\r\n").flush()
            }
            catch (Exception e) {
                e.printStackTrace()
            }
            String response = conn.getInputStream().getText()
            def json = new JsonSlurper().parseText(response)
            String responseAnswer = jsonCleaner(json.response)

            //delete the file
            file.delete()
            return responseAnswer
        }catch (Exception e){

            println("Exception in getLLMResponse "+e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
            file.delete()
            return "Error-"+e.getMessage()
        }
    }

    def getSolution(ResourceDtl resourceDtl,question,chapterId,bookId,String promptName, int noOfSimilarQuestions = 1,String serverIPAddress = null){
        JSONObject requestBody = new JSONObject()
        Prompts prompt = Prompts.findByPromptType(promptName)
        String customPrompt = prompt.basePrompt
        if("additionQuestionsCreator".equals(promptName)||"questionBankBuilder".equals(promptName)){
            BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
            if(booksMst!=null&&!"English".equals(booksMst.language)&&!"".equals(booksMst.language)&&booksMst.language!=null){
                customPrompt = customPrompt + " All questions created should be in "+booksMst.language+" language."
            }
        }
        if("additionQuestionsCreator".equals(promptName)){
           customPrompt = customPrompt.replaceAll("NOOFQUESTIONS", ""+noOfSimilarQuestions)
        }
        requestBody.put("namespace",resourceDtl.vectorStored)
        requestBody.put("resId",resourceDtl.id)
        requestBody.put("resType","userInput")
        requestBody.put("chatHistory","")
        requestBody.put("query",question)
        requestBody.put("chapterId",chapterId)
        requestBody.put("bookId",bookId)
        requestBody.put("customPrompt", customPrompt)
          URL url = new URL("http://"+serverIPAddress+":8000/api"+"/retrieveDataForBook")

        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("POST")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()
        def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
        writer.write(requestBody.toString())
        writer.flush()
        writer.close()
        def responseCode = connection.getResponseCode()
        if(responseCode==200){
            def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
            def response = reader.readLine()
            def jsonSlurper = new JsonSlurper()
            try{
                def jsonResponse = jsonSlurper.parseText(response)
                //replace all \n with <br>
                jsonResponse.answer = jsonResponse.answer.replaceAll("\\\\n", "<br>")
                return jsonResponse
            }catch (Exception e){
                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, promptName, "Exception in getSolution: "+e.getMessage(), response)
                println("Exception in getSolution: "+e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
                return null
            }

        }else{
            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, promptName, "Error in getSolution: "+responseCode, question)
            println("Error in getSolution: "+responseCode)
            return null
        }

    }

    def removeExtraCommas(String json) {
        String cleaned = json.replaceAll(",\\s*,+", ","); // Replace multiple commas with a single comma
        cleaned = cleaned.replaceAll(",\\s*]", "]");      // Remove commas just before a closing bracket

        return cleaned;
    }

    def jsonCleaner(String jsonInput){
        jsonInput = jsonInput.replaceAll("`", "")
        jsonInput = jsonInput.replaceAll("json", "")
        jsonInput = jsonInput.replaceAll("\n", "")
        jsonInput = jsonInput.replaceAll("\r", "")
        //next i want to replace ][ with comma
        jsonInput = jsonInput.replaceAll("\\]\\[", ",")
        // replace },] with }]
        jsonInput = jsonInput.replaceAll("},]", "}]")

        //replace  .^ with blank
        jsonInput = jsonInput.replaceAll("\\.\\^", "")
        jsonInput = removeExtraCommas(jsonInput)
        jsonInput = fixJsonString(jsonInput)
        return jsonInput
    }

    def createAdditionalQuestions(ResourceDtl resourceDtl,ChaptersMst chaptersMst,questionList,String serverIPAddress = null){
        println("createAdditionalQuestions for chapter " + chaptersMst.id)
        String folderPath = "supload/pdfextracts/"+chaptersMst.bookId+"/"+chaptersMst.id+"/"+resourceDtl.id+"/extractedImages/"
        List<String> additionalQuestions = []
        int noOfSimilarQuestions = 1
        if(questionList.size()<200) {
            if (questionList.size() < 100) noOfSimilarQuestions = 2
            try {
                int noOfQuestionsPerIteration = 5
                int numberOfQuestions = questionList.size()
                int currentQuestionIndex = 0
                while (currentQuestionIndex < numberOfQuestions) {
                    String inputQuestions = "The subtopic questions are \n"

                    int noOfQuestionsForLoop = noOfQuestionsPerIteration
                    if (numberOfQuestions - currentQuestionIndex < noOfQuestionsPerIteration) {
                        noOfQuestionsForLoop = numberOfQuestions - currentQuestionIndex
                    }
                    for (int i = currentQuestionIndex; i < currentQuestionIndex + noOfQuestionsForLoop; i++) {
                        inputQuestions += (i + 1) + ". " + questionList[i] + "\n"
                    }
                    inputQuestions = jsonCleaner(inputQuestions)
                    def jsonAnswer = getSolution(resourceDtl, inputQuestions, chaptersMst.id, chaptersMst.bookId, "additionQuestionsCreator", noOfSimilarQuestions,serverIPAddress)
                    if (jsonAnswer != null) {
                        try {
                            def json1 = new JsonSlurper().parseText(jsonCleaner(jsonAnswer.answer))
                            json1.each { question1 ->
                                String questionText = (question1 instanceof String) ? question1 : question1.question?.toString()
                                if(questionText && !additionalQuestions.contains(questionText)) {
                                    additionalQuestions.add(questionText)
                                }
                            }
                        } catch (Exception e) {
                            println("Exception in questionBankBuilder2.1 " + e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
                            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilderAdditionalQuestions", "Exception in parsing jsonAnswer", jsonAnswer.answer)
                            String jsonAnswerCleaned = fixJSONFromLLM(jsonAnswer.answer)
                            try {
                                def json1 = new JsonSlurper().parseText(jsonAnswerCleaned)
                                json1.each { question1 ->
                                    String questionText = (question1 instanceof String) ? question1 : question1.question?.toString()
                                    if(questionText && !additionalQuestions.contains(questionText)) {
                                        additionalQuestions.add(questionText)
                                    }
                                }
                            } catch (Exception e1) {
                                 autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilderAdditionalQuestionsFixed", "Exception in parsing jsonAnswer", jsonAnswerCleaned)
                            }
                        }
                    }
                    currentQuestionIndex += noOfQuestionsForLoop
                }
            } catch (Exception e) {
                println("Exception in questionBankBuilder1 " + e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", "Exception in parsing responseAnswer", responseAnswer)
            }
        }
        println("Total additional questions are " + additionalQuestions.size()+" for chapter " + chaptersMst.id)
        if(additionalQuestions.size()>0){
            //add additional questions to question list
            questionList.addAll(additionalQuestions)

        }
        return questionList
    }

    def addAnswersToPYQs(ResourceDtl resourceDtl,ChaptersMst chaptersMst, ResourceDtl pyqResourceDtl, params){
        println("starting addAnswersToPYQs for chapter "+chaptersMst.id)
        ObjectiveMst objectiveMst = ObjectiveMst.findByQuizId(new Integer(pyqResourceDtl.resLink))
        if(objectiveMst!=null) {
            List questionList = ObjectiveMst.findAllByQuizId(new Integer(pyqResourceDtl.resLink))
            int noOfQuestionsPerIteration = 5
            int currentQuestionIndex = 0
            List <String> solutionList = []
            try {
                int numberOfQuestions = questionList.size()
                while (currentQuestionIndex < questionList.size()) {
                    String inputQuestions = "The questions are \n"

                    int noOfQuestionsForLoop = noOfQuestionsPerIteration
                    if (numberOfQuestions - currentQuestionIndex < noOfQuestionsPerIteration) {
                        noOfQuestionsForLoop = numberOfQuestions - currentQuestionIndex
                    }
                    for (int i = currentQuestionIndex; i < currentQuestionIndex + noOfQuestionsForLoop; i++) {
                        inputQuestions += " " + questionList[i].id + ". " + questionList[i].question + "\n"
                    }
                    inputQuestions = jsonCleaner(inputQuestions)
                    try {
                        def jsonAnswer = getSolution(resourceDtl, inputQuestions, chaptersMst.id, chaptersMst.bookId, "solutionCreator", 1, params.serverIPAddress)
                        solutionList.add(jsonAnswer.answer)
                        currentQuestionIndex += noOfQuestionsForLoop
                    }catch (Exception e){
                        println("Exception in solutionCreator for chapter " + chaptersMst.id + " and the exception is " + e.toString())
                        autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "solutionCreator", "Exception in solutionCreator", inputQuestions)
                        String jsonAnswerCleaned
                        try {
                            jsonAnswerCleaned = fixJSONFromLLM(inputQuestions)
                            def jsonAnswer = getSolution(resourceDtl, jsonAnswerCleaned, chaptersMst.id, chaptersMst.bookId, "solutionCreator", 1, params.serverIPAddress)
                            solutionList.add(jsonAnswer.answer)
                            currentQuestionIndex += noOfQuestionsForLoop
                            println("Exception in solutionCreator for chapter " + chaptersMst.id + " and the exception is " + e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
                        }catch (Exception e1){
                            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "solutionCreatorFixed", "Exception in solutionCreator", jsonAnswerCleaned)
                            currentQuestionIndex += noOfQuestionsForLoop
                        }
                    }
                }
            } catch (Exception e) {
                println("Exception in parsing responseAnswer5 " + e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
            }

            //loop solutionList and add the questions and answers to ObjectiveMst

            for(String solution:solutionList){
                def jsonAnswerList
                try {
                    try {
                        jsonAnswerList = new JsonSlurper().parseText(jsonCleaner(solution))
                    } catch (Exception e) {
                        autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", "Exception in parsing jsonAnswer in solution part " + e.toString(), jsonCleaner(solution))
                        solution = fixJSONFromLLM(solution)
                        jsonAnswerList = new JsonSlurper().parseText(jsonCleaner(solution))
                    }

                    jsonAnswerList.each { json1 ->
                         ObjectiveMst om = ObjectiveMst.findById(new Integer("" + json1.questionNo))
                        if(om!=null){
                            om.answer = json1.solution
                            om.difficultylevel = json1.difficultyLevel
                            om.qType = json1.questionType
                            om.answerDescription = json1.explanation
                            om.bloomType = json1.bloomLevel
                            om.save(failOnError: true, flush: true)
                            if("true".equals(""+json1.hasFormula)){
                                fixQuestion(""+om.id, "subjective")
                            }
                        }
                    }
                }catch (Exception e){
                    autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", "Exception in parsing jsonAnswer in solution part " + e.toString(), jsonCleaner(solution))
                }
            }

        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def questionBankBuilder(params){
        ResourceDtl resourceDtl = ResourceDtl.findById(new Long(params.resId))
        ChaptersMst chaptersMst = ChaptersMst.findById(resourceDtl.chapterId)
        println("questionBankBuilder for chapter " + chaptersMst.id)
        //let us first check if the PYQs are created


        String promptText
        int numberOfMcqs=0

        //find if there is a resourceDtl instance for the given chapterId and resourceName starting with "QuestionBank"
        ResourceDtl questionBankResourceDtl = ResourceDtl.findByChapterIdAndResourceNameLike(chaptersMst.id,"QuestionBank%")
        int noOfQuestionsPerIteration = 5
        ResourceDtl pyqResourceDtl = ResourceDtl.findByChapterIdAndResourceName(chaptersMst.id,"PYQs")
        println("pyqResourceDtl: " + pyqResourceDtl)
        if(pyqResourceDtl!=null) {
            ObjectiveMst objectiveMst = ObjectiveMst.findByQuizId(new Integer(pyqResourceDtl.resLink))
            if (objectiveMst != null&&objectiveMst.answer==null){
                //call the function to add answers to PYQs
                String extractPath = resourceDtl.extractPath
                if (!extractPath || extractPath.trim().isEmpty()) {
                    extractPath = "supload/pdfextracts/"+chaptersMst.bookId+"/"+chaptersMst.id+"/"+resourceDtl.id+"/extractedImages/"+resourceDtl.id+".txt"
                }
                File sourceFile = new File(grailsApplication.config.grails.basedir.path + "/" + extractPath)
                if(!sourceFile.exists()){
                    //create the vector file it is not present
                    storePdfVectors(params)
                }
                addAnswersToPYQs(resourceDtl,chaptersMst,pyqResourceDtl,params)
            }
        }
        if(questionBankResourceDtl==null) {

            List responseList = null
            ResourceDtl qaResourceDtl, mcqResourceDtl
            List<String> questionList = []
            try {
                //first check if the question bank file exists
                String extractPath = resourceDtl.extractPath
                if (!extractPath || extractPath.trim().isEmpty()) {
                    extractPath = "supload/pdfextracts/"+chaptersMst.bookId+"/"+chaptersMst.id+"/"+resourceDtl.id+"/extractedImages/"+resourceDtl.id+".txt"
                }
                File sourceFile = new File(grailsApplication.config.grails.basedir.path + "/" + extractPath)
                if(!sourceFile.exists()){
                    //create the vector file it is not present
                    storePdfVectors(params)
                }

                String folderPath = extractPath.substring(0, extractPath.lastIndexOf("/"))
                File questionBankFile = new File(grailsApplication.config.grails.basedir.path + "/" + folderPath + "/questionBank.txt")
                if(questionBankFile.exists()) questionBankFile.delete()
                    //get the metadata from file
                    File metadataFile = new File(grailsApplication.config.grails.basedir.path + "/" + folderPath + "/chapterMetadata" + chaptersMst.id + ".txt")
                    String metadataString = metadataFile.text
                    //replace all single back slash with double back slash
                    def json
                    try {
                        metadataString = jsonCleaner(metadataString)
                        json = new JsonSlurper().parseText(metadataString)
                    } catch (Exception e) {
                        metadataString = fixJSONFromLLM(metadataString)
                        json = new JsonSlurper().parseText(metadataString)
                        println("Exception in questionBankBuilder0 " + e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
                        autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", "Exception in parsing metadata", metadataString)
                    }
// Now access the subtopics list
                    def subtopics = json.metadata.subtopics

                    Prompts prompts = Prompts.findByPromptType("questionBankBuilder")
                    Prompts mcqPrompts = Prompts.findByPromptType("questionBankBuilderMCQ")
                    // Construct full file path, get the path till the file name in the resourceDtl.extractPath
                    String filePath = grailsApplication.config.grails.basedir.path + "/" + extractPath
                    File textFile = new File(filePath)
                    String fileContent = textFile.text
                    String question, answer
                    HashMap questionMap = new HashMap()
                    String subTopicName
                    int subTopicNumber = 1
                    println("Number of subtopics are " + subtopics.size()+" for chapter " + chaptersMst.id)
                    subtopics.each { subtopic ->
                        try {
                            println("Question generation started for subtopic " + subTopicNumber + " for chapter " + chaptersMst.id)
                            String subTopicText = "" + subtopic
                            subTopicText = jsonCleaner(subTopicText)
                            promptText = prompts.basePrompt
                            promptText = promptText + " " + "\n" + subTopicText
                            promptText = jsonCleaner(promptText)

                            String responseAnswer = getLLMResponse(resourceDtl, fileContent, promptText, params.serverIPAddress)
                            println("Got the response for subtopic " + subTopicNumber + " for chapter " + chaptersMst.id)
                            if (responseAnswer.startsWith("Error-")) {
                                println("Error in questionBankBuilder for subtopic " + subTopicNumber + " for chapter " + chaptersMst.id)
                                String errorMessage = responseAnswer.substring(6)
                                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", errorMessage, promptText)
                                subTopicText = fixJSONFromLLM(subTopicText)
                                promptText = prompts.basePrompt
                                promptText = promptText.replaceAll("SUBTOPICMETADATA", "" + subTopicText)
                                try {
                                    responseAnswer = getLLMResponse(resourceDtl, fileContent, promptText, params.serverIPAddress)
                                    responseList = new JsonSlurper().parseText(responseAnswer)
                                    for (int i = 0; i < responseList.size(); i++) {
                                        //check if this question is already in the question list
                                        def questionItem = responseList[i]
                                        String questionText = (questionItem instanceof String) ? questionItem : questionItem.question?.toString()
                                        if (questionText && questionList.contains(questionText)) {
                                            continue
                                        }
                                        if (questionText) {
                                            questionList.add(questionText)
                                        }
                                    }
                                } catch (Exception e) {
                                    autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilderFixed", "Exception in getLLMResponse", promptText)
                                }
                            } else {
                                println("Parsing the response for subtopic " + subTopicNumber + " for chapter " + chaptersMst.id)
                                try {
                                    responseList = new JsonSlurper().parseText(responseAnswer)
                                    for (int i = 0; i < responseList.size(); i++) {
                                        //check if this question is already in the question list
                                        def questionItem = responseList[i]
                                        String questionText = (questionItem instanceof String) ? questionItem : questionItem.question?.toString()
                                        if (questionText && questionList.contains(questionText)) {
                                            println("Question already exists " + questionText + " for chapter " + chaptersMst.id)
                                            continue
                                        }
                                        if (questionText) {
                                            questionList.add(questionText)
                                        }
                                    }
                                } catch (Exception e) {
                                    println("Exception in questionBankBuilder1 " + e.toString().length() > 1000 ? e.toString().substring(0, 1000) : e.toString())
                                    autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", "Exception in parsing responseAnswer", responseAnswer)
                                    responseAnswer = fixJSONFromLLM(responseAnswer)
                                    try {
                                        responseList = new JsonSlurper().parseText(responseAnswer)
                                        for (int i = 0; i < responseList.size(); i++) {
                                            def questionItem = responseList[i]
                                            String questionText = (questionItem instanceof String) ? questionItem : questionItem.question?.toString()
                                            if (questionText && questionList.contains(questionText)) {
                                                continue
                                            }
                                            if (questionText) {
                                                questionList.add(questionText)
                                            }
                                        }
                                    } catch (Exception e1) {
                                        println("Exception in questionBankBuilder2 " + e1.toString())
                                        autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilderFixed", "Exception in parsing responseAnswer", responseAnswer)
                                    }
                                }
                            }

                            //get mcq questions
                            promptText = mcqPrompts.basePrompt
                            promptText = promptText + " " + "\n" + subTopicText
                            println("calling mcq prompt for subtopic " + subTopicNumber + " for chapter " + chaptersMst.id)
                            promptText = jsonCleaner(promptText)
                            responseAnswer = getLLMResponse(resourceDtl, fileContent, promptText, params.serverIPAddress)
                            if (responseAnswer.startsWith("Error-")) {
                                println("Error in questionBankBuilder for subtopic " + subTopicNumber + " for chapter " + chaptersMst.id)
                                String errorMessage = responseAnswer.substring(6)
                                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", errorMessage, promptText)
                                subTopicText = fixJSONFromLLM(subTopicText)
                                promptText = mcqPrompts.basePrompt
                                promptText = promptText.replaceAll("SUBTOPICMETADATA", "" + subTopicText)
                                try {
                                    responseAnswer = getLLMResponse(resourceDtl, fileContent, promptText, params.serverIPAddress)
                                    responseList = new JsonSlurper().parseText(responseAnswer)
                                    for (int i = 0; i < responseList.size(); i++) {
                                        def questionItem = responseList[i]
                                        String questionText = (questionItem instanceof String) ? questionItem : questionItem.question?.toString()
                                        if (questionText) {
                                            questionList.add(questionText)
                                        }
                                    }
                                } catch (Exception e) {
                                    autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilderFixed", "Exception in getLLMResponse", promptText)
                                }
                            } else {
                                println("Parsing the response for subtopic " + subTopicNumber + " for chapter " + chaptersMst.id)
                                try {
                                    responseList = new JsonSlurper().parseText(responseAnswer)
                                    for (int i = 0; i < responseList.size(); i++) {
                                        def questionItem = responseList[i]
                                        String questionText = (questionItem instanceof String) ? questionItem : questionItem.question?.toString()
                                        if (questionText) {
                                            questionList.add(questionText)
                                        }
                                    }
                                } catch (Exception e) {
                                    println("Exception in questionBankBuilder1 " + e.toString().length() > 1000 ? e.toString().substring(0, 1000) : e.toString())
                                    autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", "Exception in parsing responseAnswer", responseAnswer)
                                    responseAnswer = fixJSONFromLLM(responseAnswer)
                                    try {
                                        responseList = new JsonSlurper().parseText(responseAnswer)
                                        for (int i = 0; i < responseList.size(); i++) {
                                            def questionItem = responseList[i]
                                            String questionText = (questionItem instanceof String) ? questionItem : questionItem.question?.toString()
                                            if (questionText) {
                                                questionList.add(questionText)
                                            }
                                        }
                                    } catch (Exception e1) {
                                        println("Exception in questionBankBuilder2 " + e1.toString())
                                        autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilderFixed", "Exception in parsing responseAnswer", responseAnswer)
                                    }
                                }
                            }
                            println("Total questions are " + questionList.size() + " for subtopic " + subTopicNumber + " for chapter " + chaptersMst.id)
                            subTopicNumber++
                        }catch (Exception e1){
                            println("Exception in questionBankBuilder3 " + e1.toString())
                            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", "Exception in questionBankBuilder3", e1.toString())
                        }
                    }
                    println("Total questions are " + questionList.size()+" for chapter " + chaptersMst.id)
                    //save all the questions in the file in the same folder as the extractPath with name as "questionBank.txt" and each question delimited by ~~. Do not include quesiton number

                    if(questionList.size()<200){
                        try {
                            //close the questionBankFile
                           questionList= createAdditionalQuestions(resourceDtl, chaptersMst,questionList,params.serverIPAddress)
                        }catch (Exception e){
                            println("Exception in createAdditionalQuestions for chapter " + chaptersMst.id+" and the exception is " + e.toString())
                        }
                    }
                    questionBankFile = new File(grailsApplication.config.grails.basedir.path + "/" + folderPath + "/questionBank.txt")
                    questionBankFile.write(questionList.join("~~"))
                  int currentQuestionIndex = 0
                List <String> solutionList = []
                    try {
                        int numberOfQuestions = questionList.size()
                         while (currentQuestionIndex < questionList.size()) {
                            String inputQuestions = "The questions are \n"

                            int noOfQuestionsForLoop = noOfQuestionsPerIteration
                            if (numberOfQuestions - currentQuestionIndex < noOfQuestionsPerIteration) {
                                noOfQuestionsForLoop = numberOfQuestions - currentQuestionIndex
                            }
                            for (int i = currentQuestionIndex; i < currentQuestionIndex + noOfQuestionsForLoop; i++) {
                                inputQuestions += (i + 1) + ". " + questionList[i] + "\n"
                            }
                            inputQuestions = jsonCleaner(inputQuestions)
                            try {
                                def jsonAnswer = getSolution(resourceDtl, inputQuestions, chaptersMst.id, chaptersMst.bookId, "solutionCreator", 1, params.serverIPAddress)
                                solutionList.add(jsonAnswer.answer)
                                currentQuestionIndex += noOfQuestionsForLoop
                            }catch (Exception e){
                                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "solutionCreator", "Exception in solutionCreator", inputQuestions)
                                String jsonAnswerCleaned
                                try {
                                     jsonAnswerCleaned = fixJSONFromLLM(inputQuestions)
                                    def jsonAnswer = getSolution(resourceDtl, jsonAnswerCleaned, chaptersMst.id, chaptersMst.bookId, "solutionCreator", 1, params.serverIPAddress)
                                    solutionList.add(jsonAnswer.answer)
                                    currentQuestionIndex += noOfQuestionsForLoop
                                    println("Exception in solutionCreator for chapter " + chaptersMst.id + " and the exception is " + e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
                                }catch (Exception e1){
                                    autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "solutionCreatorFixed", "Exception in solutionCreator", jsonAnswerCleaned)
                                     currentQuestionIndex += noOfQuestionsForLoop
                                }
                            }
                        }
                    } catch (Exception e) {
                        println("Exception in parsing responseAnswer5 " + e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
                    }

               addQuestionBank(resourceDtl, chaptersMst, solutionList)


            } catch (Exception e) {
                println("Exception in parsing responseAnswer2 " + e.toString())
            }
            def json = [status: "OK", totalQuestions: questionList.size()]
            return json
        }else {
            def json = [status: "OK", message: "Question bank already created", totalQuestions: "Not Counted"]
            return json
        }
    }

    def addQuestionBank(ResourceDtl resourceDtl,ChaptersMst chaptersMst,solutionList){
        ResourceDtl mcqResourceDtl,qaResourceDtl
        BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
        int numberOfMcqs=0
        for(String solution:solutionList){
            def jsonAnswerList
            try {
                try {
                    jsonAnswerList = new JsonSlurper().parseText(jsonCleaner(solution))
                } catch (Exception e) {
                    autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", "Exception in parsing jsonAnswer in solution part " + e.toString(), jsonCleaner(solution))
                    solution = fixJSONFromLLM(solution)
                    jsonAnswerList = new JsonSlurper().parseText(jsonCleaner(solution))
                }

                jsonAnswerList.each { json1 ->
                    def json = json1
                     if (json.questionType == "MCQ") {
                        numberOfMcqs++
                        if (mcqResourceDtl == null) {
                            QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
                            quizIdGenerator.save()

                            mcqResourceDtl = new ResourceDtl()
                            mcqResourceDtl.resLink = quizIdGenerator.id
                            mcqResourceDtl.createdBy = "System"
                            mcqResourceDtl.resType = "Multiple Choice Questions"
                            mcqResourceDtl.chapterId = resourceDtl.chapterId
                            mcqResourceDtl.resourceName = "QuestionBank MCQs"
                            mcqResourceDtl.gptResourceType = "mcq"
                            mcqResourceDtl.save(failOnError: true, flush: true)

                            GptDefaultCreateLog gptDefaultCreateLog = GptDefaultCreateLog.findByReadingMaterialResIdAndPromptType(resourceDtl.id, "mcq")
                            if (gptDefaultCreateLog == null) {
                                gptDefaultCreateLog = new GptDefaultCreateLog(resId: mcqResourceDtl.id, promptType: "mcq", prompt: "Create MCQs (Multiple Choice Questions)", response: "MCQ",
                                        readingMaterialResId: resourceDtl.id, username: "System", promptLabel: "Create MCQs (Multiple Choice Questions)")
                                gptDefaultCreateLog.save(failOnError: true, flush: true)
                            } else {
                                gptDefaultCreateLog.resId = mcqResourceDtl.id
                                gptDefaultCreateLog.save(failOnError: true, flush: true)
                            }
                        }
                        // Handle correctAnswer as either Integer (1,2,3,4) or String ("option1","option2","option3","option4")
                        String correctAnswerStr = json.correctAnswer.toString()
                        boolean isOption1 = correctAnswerStr.equals("option1") || correctAnswerStr.equals("1")
                        boolean isOption2 = correctAnswerStr.equals("option2") || correctAnswerStr.equals("2")
                        boolean isOption3 = correctAnswerStr.equals("option3") || correctAnswerStr.equals("3")
                        boolean isOption4 = correctAnswerStr.equals("option4") || correctAnswerStr.equals("4")

                        ObjectiveMst om = new ObjectiveMst(quizId: new Integer(mcqResourceDtl.resLink), quizType: "MCQ", question: fixFormulas(json.questionText),
                                answer: fixFormulas(json.solution),
                                difficultylevel: json.difficultyLevel, qType: json.questionType, answerDescription: fixFormulas(json.explanation),
                                bloomType: json.bloomLevel,
                                option1: json.option1,
                                option2: json.option2,
                                option3: json.option3,
                                option4: json.option4,
                                answer1: isOption1 ? "Yes" : null,
                                answer2: isOption2 ? "Yes" : null,
                                answer3: isOption3 ? "Yes" : null,
                                answer4: isOption4 ? "Yes" : null)
                        om.save(failOnError: true, flush: true)

                         if(booksMst!=null&&!"English".equals(booksMst.language)&&!"".equals(booksMst.language)&&booksMst.language!=null){
                             fixLanguage(""+om.id,"mcq",booksMst.language)
                         }
                         if("true".equals(""+json.hasFormula)){
                             fixQuestion(""+om.id, "mcq")
                         }

                    } else {
                        if (qaResourceDtl == null) {
                            QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
                            quizIdGenerator.save()

                            qaResourceDtl = new ResourceDtl()
                            qaResourceDtl.resLink = quizIdGenerator.id
                            qaResourceDtl.createdBy = "System"
                            qaResourceDtl.resType = "QA"
                            qaResourceDtl.chapterId = resourceDtl.chapterId
                            qaResourceDtl.resourceName = "QuestionBank QnA"
                            qaResourceDtl.gptResourceType = "qna"
                            qaResourceDtl.save(failOnError: true, flush: true)
                            GptDefaultCreateLog gptDefaultCreateLog = GptDefaultCreateLog.findByReadingMaterialResIdAndPromptType(resourceDtl.id, "qna")
                            if (gptDefaultCreateLog == null) {
                                gptDefaultCreateLog = new GptDefaultCreateLog(resId: qaResourceDtl.id, promptType: "qna", prompt: "Create Question & Answers", response: "QA",
                                        readingMaterialResId: resourceDtl.id, username: "System", promptLabel: "Create Question & Answers")
                                gptDefaultCreateLog.save(failOnError: true, flush: true)
                            } else {
                                gptDefaultCreateLog.resId = qaResourceDtl.id
                                gptDefaultCreateLog.save(failOnError: true, flush: true)
                            }
                        }
                        ObjectiveMst om = new ObjectiveMst(quizId: new Integer(qaResourceDtl.resLink), quizType: "QA", question: fixFormulas(json.question),
                                answer: fixFormulas(json.solution),
                                difficultylevel: json.difficultyLevel, qType: json.questionType, answerDescription: fixFormulas(json.explanation),
                                bloomType: json.bloomLevel)
                        om.save(failOnError: true, flush: true)

                         if(booksMst!=null&&!"English".equals(booksMst.language)&&!"".equals(booksMst.language)&&booksMst.language!=null){
                             fixLanguage(""+om.id,"subjective",booksMst.language)
                         }
                         if("true".equals(""+json.hasFormula)){
                             fixQuestion(""+om.id, "subjective")
                         }
                    }
                }
            } catch (Exception e) {
                println("Exception in questionBankBuilder " + e.toString())
                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilderFixed", "Exception in parsing jsonAnswer in solution part " + e.toString(), ""+solution)

            }
        }
    }

    String fixJsonString(String inputJson) {
        // Replace LaTeX-specific sequences with properly escaped versions
        String fixedJson = inputJson
                .replace("\\\\\\\\", "FOURBACKSLASH")
                .replace("\\\\", "TWOBACKSLASH")
                .replace("\\", "ONEBACKSLASH")
        fixedJson = fixedJson.replace("FOURBACKSLASH", "\\\\")
                .replace("TWOBACKSLASH", "\\\\")
                .replace("ONEBACKSLASH", "\\\\")
        return fixedJson;

    }

    def storePdfVectors(params){
        def chapterId = params.chapterId
        if(params.resId==null){
            List readingMaterials = ResourceDtl.findAllByChapterIdAndResTypeAndSharingIsNullAndGptResourceTypeIsNull(new Integer(""+chapterId),"Notes", [sort: "id", order: "asc"])
            if(readingMaterials.size()>0)
                params.put("resId",""+readingMaterials[0].id)
            else {
                def json = [status: "Error", message: "No PDF found for this chapter"]
                return json
            }
        }
        def resId = params.resId
        ResourceDtl documentInstance = dataProviderService.getResourceDtl(new Long(resId))
        if (documentInstance == null) {
            def json = [status: "Error", message: "Document not found."]
            return json

        } else {
            try {
                String namespace
                int resCode
                if(documentInstance.vectorStored==null||documentInstance.extractPath==null||"".equals(documentInstance.extractPath)) {
                    println("have to create vectors " + chapterId)
                    File pdfFile = new File(grailsApplication.config.grails.basedir.path + "/" + documentInstance.resLink)
                    if(!pdfFile.exists()){
                        println("pdf file does not exist for chapter id " + chapterId)
                       // get the content of the directory and see if there is any pdf file in it. if yes, then use that file.
                        File dir = new File(grailsApplication.config.grails.basedir.path + "/" + documentInstance.resLink.substring(0, documentInstance.resLink.lastIndexOf("/")))
                        dir.listFiles().each { file ->
                            if(file.name.endsWith(".pdf")){
                                println("found pdf file " + file.name+" for chapter id " + chapterId)
                                documentInstance.resLink = documentInstance.resLink.substring(0, documentInstance.resLink.lastIndexOf("/"))+"/"+file.name
                                documentInstance.save(failOnError: true, flush: true)
                                pdfFile = file
                            }
                        }

                    }


                    String index = promptService.getIndex("users")
                    namespace = index + "_" + chapterId + "_" + resId
                    String filePath = documentInstance.resLink
                    resCode = newUserPDFCreation( filePath, namespace,params.serverIPAddress)

                }else{
                    namespace = documentInstance.vectorStored
                    resCode = 200
                }
                def res = ["status":"OK","message":"PDF Vectors stored successfully","resCode":resCode,namespace: namespace,resId:documentInstance.id]
                return res
            }catch(Exception e){
                def err = ["status":"Error","message":e.message]
                return err
            }
        }
    }

    def newUserPDFCreation(filePath,namespace,String serverIPAddress){
        URL url = new URL("http://"+serverIPAddress+":8000/api"+"/processPDFVectorNew")
        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("POST")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()
        def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))

        def json = new JsonBuilder([namespace: namespace, filePath: filePath])
        writer.write(json.toString())
        writer.flush()
        writer.close()
        def responseCode = connection.getResponseCode()
        return responseCode
    }





    @Transactional(propagation = Propagation.REQUIRES_NEW)

    int getNextChapterForProcessing(int serverIndex){

        KeyValueMst runGPTJob = KeyValueMst.findByKeyName("runGPTJob")
        if(runGPTJob!=null&&runGPTJob.keyValue.equals("true")) {
            int noOfParallelTasks = 3
            KeyValueMst keyValueMst = KeyValueMst.findByKeyName("numberOfParallelAutoGPTTasks")
            if (keyValueMst != null) {
                noOfParallelTasks = Integer.parseInt(keyValueMst.keyValue)
            }
            def autogptLogs = AutogptLog.findAllByGptStatusAndServerIndex("running", serverIndex)
            if (autogptLogs.size() < noOfParallelTasks) {
                String sql  = " SELECT id " +
                        " FROM wslog.autogpt_log " +
                        " WHERE gpt_status ='vectorCreated' order by id asc limit 1"
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
                def sql1 = new SafeSql(dataSource)
                def results = sql1.rows(sql)
                 if(results.size()>0){
                    AutogptLog autogptLog = AutogptLog.findById(new Long(""+results[0].id))
                    autogptLog.gptStatus = "running"
                    autogptLog.dateStarted = new Date()
                    autogptLog.dateCompleted = null
                    autogptLog.serverIndex = serverIndex
                    autogptLog.save(failOnError: true, flush: true)
                    return autogptLog.chapterId.intValue()
                }
                else {
                    return 0
                }
            }
            else {
                return 0
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)

    int getNextChapterForPDFProcessing(int serverIndex){

        KeyValueMst runGPTJob = KeyValueMst.findByKeyName("runGPTJob")
        if(runGPTJob!=null&&runGPTJob.keyValue.equals("true")) {

               AutogptLog autogptLog = AutogptLog.findByGptStatusAndServerIndex("vectorCreating", serverIndex)
               if(autogptLog==null) {

                   String sql = " SELECT id " +
                           " FROM wslog.autogpt_log " +
                           " WHERE gpt_status is null order by id asc limit 1"
                   def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
                   def sql1 = new SafeSql(dataSource)
                   def results = sql1.rows(sql)
                   if (results.size() > 0) {
                       autogptLog = AutogptLog.findById(new Long("" + results[0].id))
                       autogptLog.gptStatus = "vectorCreating"
                       autogptLog.dateStarted = new Date()
                       autogptLog.serverIndex = serverIndex
                       autogptLog.save(failOnError: true, flush: true)
                       return autogptLog.chapterId.intValue()
                   } else {
                       return 0
                   }

               }else{
                   return 0
               }
        }
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def deleteEmbeddings(ResourceDtl resourceDtl) {
        if (resourceDtl.vectorStored != null) {
            JSONObject requestBody = new JSONObject()

            requestBody.put("namespace", resourceDtl.vectorStored)
             URL url = new URL(grailsApplication.config.grails.aiserver.url + "/delete-namespace")

            HttpURLConnection connection = (HttpURLConnection) url.openConnection()
            connection.setRequestMethod("POST")
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setDoOutput(true)
            connection.setDoInput(true)
            connection.connect()
            def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
            writer.write(requestBody.toString())
            writer.flush()
            writer.close()
            def responseCode = connection.getResponseCode()
            if (responseCode == 200) {
                resourceDtl.vectorStored = null
                resourceDtl.save(failOnError: true, flush: true)
                def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                def response = reader.readLine()
                def jsonSlurper = new JsonSlurper()
                def jsonResponse = jsonSlurper.parseText(response)
                return jsonResponse
            } else {
                return null
            }

        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def checkPendingJobs(){
        String sql  = " SELECT id " +
                " FROM wslog.autogpt_log\n" +
                " WHERE TIMESTAMPDIFF(HOUR, date_started, SYSDATE()) > 3\n" +
                " and gpt_status='running'"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        results.each { log ->
            AutogptLog autogptLog = AutogptLog.findById(new Long(log.id))
            //delete
            autogptLog.delete(flush: true)
        }
    }

    def getChapterResources(String bookId){
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        if(booksMst.packageBookIds!=null) bookId = booksMst.packageBookIds

           List chapters = new ArrayList()
           String[] bookIds = bookId.split(",")
            for(int i=0;i<bookIds.length;i++){
                String tempBookId = bookIds[i]
                BooksDtl booksDtl = BooksDtl.findByBookId(new Integer(tempBookId))
                if(booksDtl!=null&&booksDtl.masterBookId!=null) tempBookId = ""+booksDtl.masterBookId
                chapters.addAll(ChaptersMst.findAllByBookId(new Integer(tempBookId)))
            }

        List chapterDetails = []
        chapters.each { chapter ->
            int noOfExercises = 0
            int noOfQB = 0
            int noOfQBMCQs = 0

            ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Integer(""+chapter.id),"QA","Exercise Solutions")
            if(resourceDtl!=null) {
                noOfExercises = ObjectiveMst.countByQuizId(new Integer(resourceDtl.resLink));
            }

            resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Integer(""+chapter.id),"Multiple Choice Questions","QuestionBank MCQs")
            if(resourceDtl!=null) {
                noOfQBMCQs = ObjectiveMst.countByQuizId(new Integer(resourceDtl.resLink));
            }
            resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Integer(""+chapter.id),"QA","QuestionBank QnA")
            if(resourceDtl!=null) {
                noOfQB = ObjectiveMst.countByQuizId(new Integer(resourceDtl.resLink));
            }
            //check if PYQs are created
            resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Integer(""+chapter.id),"QA","PYQs")
            if(resourceDtl!=null) {
                noOfQB += ObjectiveMst.countByQuizId(new Integer(resourceDtl.resLink));
            }
            chapterDetails << [chapterId: chapter.id, chapterName: chapter.name, noOfExercises: noOfExercises, noOfQB: noOfQB, noOfQBMCQs: noOfQBMCQs]
        }
        return chapterDetails
    }

    def fixFormulas(String input) {
        return input
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def updateAutoGPTLog(Long chapterId,String gptStatus){
        AutogptLog autogptLog = AutogptLog.findByChapterId(chapterId)
        if(autogptLog!=null){
            autogptLog.gptStatus = gptStatus
            autogptLog.dateCompleted = new Date()
            autogptLog.save(failOnError: true, flush: true)
        }
    }

    def fixCurlyBrackets(String input) {
        //first replace all { with the string SINGLEFORWARD
        return input

    }

    def fixQuestion(String objId,String questionType){
        ObjectiveMst objectiveMst = ObjectiveMst.findById(new Integer(objId))
        Prompts prompts = Prompts.findByPromptType("questionFixer_"+questionType)


        String prompt
        if("mcq".equals(questionType)){
            prompt = prompts.basePrompt + " \n question:\n"+objectiveMst.question+
                    "\n answer:\n"+objectiveMst.answer+"\n"+
                    "\n option1:\n"+objectiveMst.option1+"\n"+
                    "\n option2:\n"+objectiveMst.option2+"\n"+
                    "\n option3:\n"+objectiveMst.option3+"\n"+
                    "\n option4:\n"+objectiveMst.option4+"\n"+
                    "\n answer Explanation:\n"+objectiveMst.answerDescription+"\n"
        }else{
            prompt = prompts.basePrompt + " \n question:\n"+objectiveMst.question+"\n"+
                    "\n answer:\n"+objectiveMst.answer+"\n"+
                    "\n answer Explanation:\n"+objectiveMst.answerDescription+"\n"
        }

        def fixResponse = getQuestionFix(prompt, objId)
         def jsonResponse = new JsonSlurper().parseText( jsonCleaner(fixResponse.response))
        if(jsonResponse!=null){
            if("mcq".equals(questionType)){
                objectiveMst.question = jsonResponse.question
                objectiveMst.option1 = jsonResponse.option1
                objectiveMst.option2 = jsonResponse.option2
                objectiveMst.option3 = jsonResponse.option3
                objectiveMst.option4 = jsonResponse.option4
                objectiveMst.answerDescription = jsonResponse.answerDescription
                objectiveMst.answer = jsonResponse.correctAnswer

                // Handle correctAnswer as either Integer (1,2,3,4) or String ("option1","option2","option3","option4")
                String correctAnswerStr = jsonResponse.correctAnswer.toString()
                objectiveMst.answer1 = (correctAnswerStr.equals("option1") || correctAnswerStr.equals("1")) ? "Yes" : null
                objectiveMst.answer2 = (correctAnswerStr.equals("option2") || correctAnswerStr.equals("2")) ? "Yes" : null
                objectiveMst.answer3 = (correctAnswerStr.equals("option3") || correctAnswerStr.equals("3")) ? "Yes" : null
                objectiveMst.answer4 = (correctAnswerStr.equals("option4") || correctAnswerStr.equals("4")) ? "Yes" : null
            }else{
                objectiveMst.question = jsonResponse.question
                objectiveMst.answerDescription = jsonResponse.answerDescription
                objectiveMst.answer = jsonResponse.answer
            }

             objectiveMst.save(failOnError: true, flush: true)

        }
        return objectiveMst
    }

    def getQuestionFix(String questionInput,String objId){
        JSONObject requestBody = new JSONObject()
        requestBody.put("prompt", questionInput)
        URL url = new URL(grailsApplication.config.grails.aiserver.url+"/chat-completion")

        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("POST")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()
        def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
        writer.write(requestBody.toString())
        writer.flush()
        writer.close()
        def responseCode = connection.getResponseCode()
        if(responseCode==200){
            def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
            def response = reader.readLine()
            def jsonSlurper = new JsonSlurper()
            try{
                def jsonResponse = jsonSlurper.parseText(response)
                return jsonResponse
            }catch (Exception e){
                 println("Exception in getQuestionFix: "+e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
                return null
            }

        }else{
            println("Error in getQuestionFix: "+responseCode)
            return null
        }

    }

    String fixJSONFromLLM(String input) {
       Prompts prompts = Prompts.findByPromptType("jsonFixer")
       String prompt = prompts.basePrompt + " \n "+input
        String output = getQuestionFix(prompt, "-1").response
        return output
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def runAutoGPT(int chapterId,String serverIPAddress) {
        def params = new HashMap()
        params.put("serverIPAddress", serverIPAddress)
        params.put("chapterId", "" + chapterId)
        ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndSharingIsNullAndGptResourceTypeIsNull(new Integer(params.chapterId), "Notes")
        if (resourceDtl != null) {
            params.put("resId", "" + resourceDtl.id)
        } else {
            def json = [status: "Error", message: "No PDF found for this chapter"]
            return json
        }
        getChapterMetaData(params)
       exerciseCollector(params)
        questionBankBuilder(params)
       deleteEmbeddings(resourceDtl)
       updateAutoGPTLog(new Long("" + chapterId), "completed")
        def json = [status: "OK", message: "AutoGPT task completed"]
        return json
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def autoGPTPdfProcessorRunner(params) {
                def json =  storePdfVectors(params)
                updateAutoGPTLog(new Long("" + params.chapterId), "vectorCreated")

        return "Completed"
    }

    def fixLanguage(String objId,String questionType,String language){
        ObjectiveMst objectiveMst = ObjectiveMst.findById(new Integer(objId))
        Prompts prompts = Prompts.findByPromptType("languageCorrections_"+questionType)


        String prompt
        if("mcq".equals(questionType)){
            prompt = prompts.basePrompt + " \n question:\n"+objectiveMst.question+
                    "\n answer:\n"+objectiveMst.answer+"\n"+
                    "\n option1:\n"+objectiveMst.option1+"\n"+
                    "\n option2:\n"+objectiveMst.option2+"\n"+
                    "\n option3:\n"+objectiveMst.option3+"\n"+
                    "\n option4:\n"+objectiveMst.option4+"\n"+
                    "\n answer Explanation:\n"+objectiveMst.answerDescription+"\n"
        }else{
            prompt = prompts.basePrompt + " \n question:\n"+objectiveMst.question+"\n"+
                    "\n answer:\n"+objectiveMst.answer+"\n"+
                    "\n answer Explanation:\n"+objectiveMst.answerDescription+"\n"
        }
        prompt = prompt.replaceAll("BOOKLANGUAGE", language)

        def fixResponse = getQuestionFix(prompt, objId)
        def jsonResponse = new JsonSlurper().parseText( jsonCleaner(fixResponse.response))
        if(jsonResponse!=null){
            if("mcq".equals(questionType)){
                objectiveMst.question = jsonResponse.question
                objectiveMst.option1 = jsonResponse.option1
                objectiveMst.option2 = jsonResponse.option2
                objectiveMst.option3 = jsonResponse.option3
                objectiveMst.option4 = jsonResponse.option4
                objectiveMst.answerDescription = jsonResponse.answerDescription
                objectiveMst.answer = jsonResponse.correctAnswer

                // Handle correctAnswer as either Integer (1,2,3,4) or String ("option1","option2","option3","option4")
                String correctAnswerStr = jsonResponse.correctAnswer.toString()
                objectiveMst.answer1 = (correctAnswerStr.equals("option1") || correctAnswerStr.equals("1")) ? "Yes" : null
                objectiveMst.answer2 = (correctAnswerStr.equals("option2") || correctAnswerStr.equals("2")) ? "Yes" : null
                objectiveMst.answer3 = (correctAnswerStr.equals("option3") || correctAnswerStr.equals("3")) ? "Yes" : null
                objectiveMst.answer4 = (correctAnswerStr.equals("option4") || correctAnswerStr.equals("4")) ? "Yes" : null
            }else{
                objectiveMst.question = jsonResponse.question
                objectiveMst.answerDescription = jsonResponse.answerDescription
                objectiveMst.answer = jsonResponse.answer
            }

            objectiveMst.save(failOnError: true, flush: true)

        }
        return objectiveMst
    }



}
