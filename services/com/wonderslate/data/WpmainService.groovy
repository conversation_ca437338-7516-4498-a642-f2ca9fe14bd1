package com.wonderslate.data

import grails.transaction.Transactional
import com.wonderslate.data.ObjectiveMst
import com.wonderslate.data.ResourceDtl
import com.wonderslate.sqlutil.SafeSql

@Transactional
class WpmainService {
    def grailsApplication

    def serviceMethod() {

    }

    def getExerciseSolutions(Long chapterId) {
        String sql = "SELECT om.id, om.question, om.answer, om.answer_description answerDescription, om.gpt_explanation gptExplanation, om.q_type qType " +
                "FROM resource_dtl rd " +
                "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                "WHERE rd.chapter_id = " + chapterId + " " +
                "AND rd.resource_name = 'Exercise Solutions' " +
                "AND rd.res_type = 'QA' " +
                "ORDER BY om.id"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        return results
    }



    def getQuestionBankData(Long chapterId) {
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)

        // Get QnA questions
        String qnaQuery = "SELECT om.id, om.question, om.answer, om.answer_description answerDescription, om.gpt_explanation gptExplanation, om.q_type qType, " +
                "om.option1, om.option2, om.option3, om.option4, om.option5 " +
                "FROM resource_dtl rd " +
                "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                "WHERE rd.chapter_id = " + chapterId + " " +
                "AND rd.resource_name in ('QuestionBank QnA', 'PYQs') " +
                "AND rd.res_type = 'QA' " +
                "ORDER BY om.q_type, om.id"

        // Get MCQ questions
        String mcqQuery = "SELECT om.id, om.question, om.answer, om.answer_description answerDescription, om.gpt_explanation gptExplanation, om.q_type qType, " +
                "om.option1, om.option2, om.option3, om.option4, om.option5,om.answer1,om.answer2,om.answer3,om.answer4,om.answer5, " +
                "rd.res_link as quizId, rd.id as resId " +
                "FROM resource_dtl rd " +
                "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                "WHERE rd.chapter_id = " + chapterId + " " +
                "AND rd.resource_name = 'QuestionBank MCQs' " +
                "AND rd.res_type = 'Multiple Choice Questions' " +
                "ORDER BY om.id"

        def qnaResults = sql1.rows(qnaQuery)
        def mcqResults = sql1.rows(mcqQuery)

        // Group QnA results by qType
        def groupedQnA = qnaResults.groupBy { it.qType }

        return [
            qnaQuestions: groupedQnA,
            mcqQuestions: mcqResults
        ]
    }

    def getQuestionTypeCounts(Long chapterId) {
        String sql = "SELECT om.q_type qType, COUNT(*) as count " +
                "FROM resource_dtl rd " +
                "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                "WHERE rd.chapter_id = " + chapterId + " " +
                "AND rd.resource_name IN ('QuestionBank QnA', 'QuestionBank MCQs') " +
                "AND rd.res_type IN ('QA', 'Multiple Choice Questions') " +
                "GROUP BY om.q_type"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        def counts = [:]
        results.each { row ->
            //if qType is MCQ change it to Multiple Choice Questions
            if (row.qType == 'MCQ') {
                row.qType = 'Multiple Choice Questions'
            }
            counts[row.qType] = row.count
        }

        // Check for Previous Year Questions separately
        String pyqSql = "SELECT COUNT(*) as count " +
                "FROM resource_dtl rd " +
                "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                "WHERE rd.chapter_id = " + chapterId + " " +
                "AND rd.resource_name = 'PYQs' " +
                "AND rd.res_type = 'QA'"

        def pyqResults = sql1.rows(pyqSql)
        if (pyqResults && pyqResults[0].count > 0) {
            counts['Previous Year Questions'] = pyqResults[0].count
        }

        return counts
    }

    def getQuestionTypeData(Long chapterId, String questionType) {
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)

        if (questionType == 'Multiple Choice Questions') {
            // Get MCQ questions
            String mcqQuery = "SELECT om.id, om.question, om.answer, om.answer_description answerDescription, om.gpt_explanation gptExplanation, om.q_type qType, " +
                    "om.option1, om.option2, om.option3, om.option4, om.option5,om.answer1,om.answer2,om.answer3,om.answer4,om.answer5, " +
                    "rd.res_link as quizId, rd.id as resId " +
                    "FROM resource_dtl rd " +
                    "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                    "WHERE rd.chapter_id = " + chapterId + " " +
                    "AND rd.resource_name = 'QuestionBank MCQs' " +
                    "AND rd.res_type = 'Multiple Choice Questions' " +
                    "ORDER BY om.id"

            def mcqResults = sql1.rows(mcqQuery)
            return [mcqQuestions: mcqResults]
        } else if (questionType == 'Previous Year Questions') {
            // Get PYQ questions
            String pyqQuery = "SELECT om.id, om.question, om.answer, om.answer_description answerDescription, om.gpt_explanation gptExplanation, om.q_type qType, " +
                    "om.explain_link explainLink " +
                    "FROM resource_dtl rd " +
                    "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                    "WHERE rd.chapter_id = " + chapterId + " " +
                    "AND rd.resource_name = 'PYQs' " +
                    "AND rd.res_type = 'QA' " +
                    "ORDER BY om.id"

            def pyqResults = sql1.rows(pyqQuery)

            // Process explainLink to extract university and year information
            pyqResults.each { result ->
                if (result.explainLink) {
                    def parts = result.explainLink.split(',')
                    result.university = parts.length > 0 && parts[0] && parts[0].trim() != 'Unknown' ? parts[0].trim() : null
                    result.examYear = parts.length > 1 && parts[1] && parts[1].trim() != 'Unknown' ? parts[1].trim() : null
                } else {
                    result.university = null
                    result.examYear = null
                }
            }

            def groupedPYQ = [:]
            groupedPYQ[questionType] = pyqResults
            return [qnaQuestions: groupedPYQ]
        } else {
            // Get QnA questions for specific type
            String qnaQuery = "SELECT om.id, om.question, om.answer, om.answer_description answerDescription, om.gpt_explanation gptExplanation, om.q_type qType, " +
                    "om.option1, om.option2, om.option3, om.option4, om.option5 " +
                    "FROM resource_dtl rd " +
                    "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                    "WHERE rd.chapter_id = " + chapterId + " " +
                    "AND rd.resource_name = 'QuestionBank QnA' " +
                    "AND rd.res_type = 'QA' " +
                    "AND om.q_type = '" + questionType + "' " +
                    "ORDER BY om.id"

            def qnaResults = sql1.rows(qnaQuery)
            def groupedQnA = [:]
            groupedQnA[questionType] = qnaResults
            return [qnaQuestions: groupedQnA]
        }
    }

    def getExplanation(Long questionId) {
        ObjectiveMst question = ObjectiveMst.findById(questionId)
        return question?.gptExplanation ?: question?.answerDescription
    }

    def getBookOverviewData(Long bookId, List chaptersList) {
        def overviewData = []

        chaptersList.each { chapter ->
            def chapterData = [:]
            chapterData.chapterId = chapter.chapterId ?: chapter.id
            chapterData.chapterName = chapter.chapterName ?: chapter.name

            // Get question type counts for this chapter
            def questionTypeCounts = getQuestionTypeCounts(chapterData.chapterId as Long)

            // Get exercise solutions count
            def exerciseSolutions = getExerciseSolutions(chapterData.chapterId as Long)
            def exerciseCount = exerciseSolutions?.size() ?: 0

            chapterData.questionTypeCounts = questionTypeCounts
            chapterData.exerciseCount = exerciseCount
            chapterData.totalQuestions = questionTypeCounts.values().sum() ?: 0

            overviewData.add(chapterData)
        }

        return overviewData
    }

    def getBookLevelSummary(Long bookId, List chaptersList) {
        def bookSummary = [:]
        def totalCounts = [:]
        def totalExercises = 0

        // Initialize counters for each question type in the specified order
        def questionTypeOrder = [
            "LongAnswer",
            "ShortAnswer",
            "VeryShortAnswer",
            "AssertionReason",
            "Problem",
            "Multiple Choice Questions",
            "FillBlank",
            "TrueFalse",
            "MatchFollowing",
            "ArrangeSequence"
        ]

        questionTypeOrder.each { qType ->
            totalCounts[qType] = 0
        }

        // Aggregate data from all chapters
        chaptersList.each { chapter ->
            def chapterId = chapter.chapterId ?: chapter.id

            // Get question type counts for this chapter
            def questionTypeCounts = getQuestionTypeCounts(chapterId as Long)
            questionTypeCounts.each { qType, count ->
                if (totalCounts.containsKey(qType)) {
                    totalCounts[qType] += count
                }
            }

            // Get exercise solutions count
            def exerciseSolutions = getExerciseSolutions(chapterId as Long)
            totalExercises += exerciseSolutions?.size() ?: 0
        }

        bookSummary.questionTypeCounts = totalCounts
        bookSummary.totalExercises = totalExercises
        bookSummary.totalQuestions = totalCounts.values().sum() ?: 0
        bookSummary.totalChapters = chaptersList.size()

        return bookSummary
    }

    def getRelatedBooks(Long bookId, Long publisherId) {
        try {
            // Get related books from same publisher, excluding current book
            String sql = 'SELECT id, title, cover_image coverImage, price, listprice, rating, authors ' +
                        'FROM books_mst ' +
                        'WHERE publisher_id = ' + publisherId + ' ' +
                        'AND id != ' + bookId + ' ' +
                        'AND status = "published" ' +
                        'ORDER BY rating DESC, date_published DESC ' +
                        'LIMIT 6'

            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)
            return results
        } catch (Exception e) {
            println("Error getting related books for book ${bookId}: ${e.message}")
            return [] // Return empty list if there's an error
        }
    }

    def getTestimonials() {
        // Return dummy testimonials data
        return [
            [
                name: "Priya Sharma",
                role: "Class 12 Student",
                rating: 5,
                comment: "This AI-powered book has completely transformed my learning experience. The interactive explanations and practice questions are incredibly helpful!",
                avatar: "PS"
            ],
            [
                name: "Rajesh Kumar",
                role: "Teacher",
                rating: 5,
                comment: "As an educator, I'm impressed by the comprehensive content and AI-driven features. My students love the instant explanations and doubt clearing.",
                avatar: "RK"
            ],
            [
                name: "Anita Patel",
                role: "Parent",
                rating: 4,
                comment: "My daughter's grades have improved significantly since using this book. The structured approach and practice tests are excellent.",
                avatar: "AP"
            ],
            [
                name: "Vikram Singh",
                role: "Class 10 Student",
                rating: 5,
                comment: "The AI tutor feature is amazing! It's like having a personal teacher available 24/7. Highly recommended for all students.",
                avatar: "VS"
            ]
        ]
    }

    def getBookPricingInfo(Long bookId) {
        try {
            // Get pricing information from BookPriceDtl
            String sql = 'SELECT book_type bookType, list_price listPrice, sell_price sellPrice, currency_cd currencyCd ' +
                        'FROM wsshop.book_price_dtl ' +
                        'WHERE book_id = ' + bookId + ' ' +
                        'ORDER BY FIELD(book_type, "printbook", "eBook", "testSeries", "combo", "upgrade", "bookGPT")'

            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)

            def pricingMap = [:]
            results.each { result ->
                pricingMap[result.bookType] = [
                    listPrice: result.listPrice,
                    sellPrice: result.sellPrice,
                    currency: result.currencyCd ?: 'INR'
                ]
            }

            return pricingMap
        } catch (Exception e) {
            println("Error getting pricing info for book ${bookId}: ${e.message}")
            // Return default pricing structure
            return [
                eBook: [
                    listPrice: 299.0,
                    sellPrice: 199.0,
                    currency: 'INR'
                ]
            ]
        }
    }

    def getNameSpace(Long chapterId) {
        try{
            String sql = "SELECT res_link, vector_stored FROM resource_dtl WHERE chapter_id = " + chapterId + " AND vector_stored IS NOT NULL"
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)
            if(results.size()>0 && results[0]!=null && results[0].res_link!=null && results[0].res_link.endsWith(".pdf")) {
                return results[0].vector_stored
            }
        }catch (Exception e){
            println("Error getting namespace for chapter ${chapterId}: ${e.message}")
            return null
        }
    }

    def getResId(Long chapterId) {
        try{
            String sql = "SELECT id FROM resource_dtl WHERE chapter_id = " + chapterId + " AND res_type = 'Notes' AND res_link LIKE '%.pdf'"
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)
            if(results.size()>0 && results[0]!=null) {
                return results[0].id
            }
        }catch (Exception e){
            println("Error getting resId for chapter ${chapterId}: ${e.message}")
            return null
        }
    }

    def getQuestionsForQuestionPaper(List<Long> chapterIds, String questionType, String difficultyLevel, int numberOfQuestions) {
        try {
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new SafeSql(dataSource)

            String resourceName = questionType == 'Multiple Choice Questions' ? 'QuestionBank MCQs' : 'QuestionBank QnA'
            String resType = questionType == 'Multiple Choice Questions' ? 'Multiple Choice Questions' : 'QA'
            String chapterIdsStr = chapterIds.join(',')

            // Handle Mixed Objectives question type
            if (questionType == 'Mixed Objectives') {
                return getMixedObjectiveQuestions(chapterIds, difficultyLevel, numberOfQuestions)
            }

            String questionTypeFilter = ""
            if (questionType != 'Multiple Choice Questions') {
                questionTypeFilter = " AND om.q_type = '" + questionType + "'"
            }

            def allQuestions = []

            // First, try to get questions from the requested difficulty level
            if (difficultyLevel && difficultyLevel != 'All') {
                String sql = "SELECT om.id, om.question, om.answer, om.q_type qType, om.difficultylevel, " +
                            "om.option1, om.option2, om.option3, om.option4, om.option5, " +
                            "om.answer1, om.answer2, om.answer3, om.answer4, om.answer5, " +
                            "rd.chapter_id chapterId " +
                            "FROM resource_dtl rd " +
                            "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                            "WHERE rd.chapter_id IN (" + chapterIdsStr + ") " +
                            "AND rd.resource_name = '" + resourceName + "' " +
                            "AND rd.res_type = '" + resType + "' " +
                            "AND om.difficultylevel = '" + difficultyLevel + "' " +
                            questionTypeFilter + " " +
                            "ORDER BY RAND()"

                def primaryResults = sql1.rows(sql)
                allQuestions.addAll(primaryResults)

                // If we don't have enough questions from the requested difficulty, get from other difficulties
                if (allQuestions.size() < numberOfQuestions) {
                    int remainingQuestions = numberOfQuestions - allQuestions.size()

                    String fallbackSql = "SELECT om.id, om.question, om.answer, om.q_type qType, om.difficultylevel, " +
                                        "om.option1, om.option2, om.option3, om.option4, om.option5, " +
                                        "om.answer1, om.answer2, om.answer3, om.answer4, om.answer5, " +
                                        "rd.chapter_id chapterId " +
                                        "FROM resource_dtl rd " +
                                        "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                                        "WHERE rd.chapter_id IN (" + chapterIdsStr + ") " +
                                        "AND rd.resource_name = '" + resourceName + "' " +
                                        "AND rd.res_type = '" + resType + "' " +
                                        "AND om.difficultylevel != '" + difficultyLevel + "' " +
                                        questionTypeFilter + " " +
                                        "ORDER BY RAND() " +
                                        "LIMIT " + remainingQuestions

                    def fallbackResults = sql1.rows(fallbackSql)
                    allQuestions.addAll(fallbackResults)
                }
            } else {
                // Get questions from all difficulty levels
                String sql = "SELECT om.id, om.question, om.answer, om.q_type qType, om.difficultylevel, " +
                            "om.option1, om.option2, om.option3, om.option4, om.option5, " +
                            "om.answer1, om.answer2, om.answer3, om.answer4, om.answer5, " +
                            "rd.chapter_id chapterId " +
                            "FROM resource_dtl rd " +
                            "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                            "WHERE rd.chapter_id IN (" + chapterIdsStr + ") " +
                            "AND rd.resource_name = '" + resourceName + "' " +
                            "AND rd.res_type = '" + resType + "' " +
                            questionTypeFilter + " " +
                            "ORDER BY RAND()"

                def results = sql1.rows(sql)
                allQuestions.addAll(results)
            }

            // Ensure we return exactly the requested number of questions (or all available if less)
            if (allQuestions.size() > numberOfQuestions) {
                return allQuestions.take(numberOfQuestions)
            } else {
                return allQuestions
            }
        } catch (Exception e) {
            println("Error getting questions for question paper: ${e.message}")
            return []
        }
    }

    def getMixedObjectiveQuestions(List<Long> chapterIds, String difficultyLevel, int numberOfQuestions) {
        try {
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new SafeSql(dataSource)
            String chapterIdsStr = chapterIds.join(',')

            // Define the question types for mixed objectives
            def mixedTypes = ['Multiple Choice Questions', 'FillBlank', 'TrueFalse']
            def allQuestions = []

            // Get questions from each type
            mixedTypes.each { qType ->
                String resourceName = qType == 'Multiple Choice Questions' ? 'QuestionBank MCQs' : 'QuestionBank QnA'
                String resType = qType == 'Multiple Choice Questions' ? 'Multiple Choice Questions' : 'QA'

                String difficultyFilter = ""
                if (difficultyLevel && difficultyLevel != 'All') {
                    difficultyFilter = " AND om.difficultylevel = '" + difficultyLevel + "'"
                }

                String questionTypeFilter = ""
                if (qType != 'Multiple Choice Questions') {
                    questionTypeFilter = " AND om.q_type = '" + qType + "'"
                }

                String sql = "SELECT om.id, om.question, om.answer, om.q_type qType, om.difficultylevel, " +
                            "om.option1, om.option2, om.option3, om.option4, om.option5, " +
                            "om.answer1, om.answer2, om.answer3, om.answer4, om.answer5, " +
                            "rd.chapter_id chapterId " +
                            "FROM resource_dtl rd " +
                            "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                            "WHERE rd.chapter_id IN (" + chapterIdsStr + ") " +
                            "AND rd.resource_name = '" + resourceName + "' " +
                            "AND rd.res_type = '" + resType + "' " +
                            difficultyFilter + questionTypeFilter + " " +
                            "ORDER BY RAND()"

                def results = sql1.rows(sql)
                allQuestions.addAll(results)
            }

            // Shuffle all questions together and return the requested number
            Collections.shuffle(allQuestions)

            if (allQuestions.size() > numberOfQuestions) {
                return allQuestions.take(numberOfQuestions)
            } else {
                return allQuestions
            }
        } catch (Exception e) {
            println("Error getting mixed objective questions: ${e.message}")
            return []
        }
    }

    def getAvailableQuestionCounts(List<Long> chapterIds) {
        try {
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new SafeSql(dataSource)

            String chapterIdsStr = chapterIds.join(',')

            // Get QnA question counts by type
            String qnaQuery = "SELECT om.q_type, COUNT(*) as count " +
                            "FROM resource_dtl rd " +
                            "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                            "WHERE rd.chapter_id IN (" + chapterIdsStr + ") " +
                            "AND rd.resource_name = 'QuestionBank QnA' " +
                            "AND rd.res_type = 'QA' " +
                            "GROUP BY om.q_type"

            // Get MCQ question counts
            String mcqQuery = "SELECT 'Multiple Choice Questions' as q_type, COUNT(*) as count " +
                            "FROM resource_dtl rd " +
                            "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                            "WHERE rd.chapter_id IN (" + chapterIdsStr + ") " +
                            "AND rd.resource_name = 'QuestionBank MCQs' " +
                            "AND rd.res_type = 'Multiple Choice Questions'"

            def qnaResults = sql1.rows(qnaQuery)
            def mcqResults = sql1.rows(mcqQuery)

            def counts = [:]
            qnaResults.each { row ->
                counts[row.q_type] = row.count
            }
            mcqResults.each { row ->
                counts[row.q_type] = row.count
            }

            // Calculate Mixed Objectives count (sum of MCQ, FillBlank, and TrueFalse)
            def mixedObjectivesCount = 0
            if (counts['Multiple Choice Questions']) {
                mixedObjectivesCount += counts['Multiple Choice Questions']
            }
            if (counts['FillBlank']) {
                mixedObjectivesCount += counts['FillBlank']
            }
            if (counts['TrueFalse']) {
                mixedObjectivesCount += counts['TrueFalse']
            }
            if (mixedObjectivesCount > 0) {
                counts['Mixed Objectives'] = mixedObjectivesCount
            }

            return counts
        } catch (Exception e) {
            println("Error getting available question counts: ${e.message}")
            return [:]
        }
    }

    def generateQuestionPaper(String paperName, String paperHeader, List<Long> chapterIds, List<Map> sections, String createdBy,  Long bookId) {
        try {
            // Create question paper master record
            def quesPaperMst = new com.wonderslate.qp.QuesPaperMst(
                createdBy: createdBy,
                patternName: paperName,
                header: paperHeader,
                bookId: bookId as Integer,
                dateCreated: new Date()
            )

            if (!quesPaperMst.save(flush: true)) {
                println("Error saving QuesPaperMst: ${quesPaperMst.errors}")
                return [success: false, message: "Failed to create question paper"]
            }

            def totalMarks = 0

            // Process each section
            sections.each { section ->
                if (section.sectionHeader && section.marksPerQuestion && section.totalQuestions && section.questionType) {
                    // Get questions for this section
                    def questions = getQuestionsForQuestionPaper(
                        chapterIds,
                        section.questionType,
                        section.difficultyLevel ?: 'All',
                        section.totalQuestions as Integer
                    )

                    if (questions.size() > 0) {
                        // Create comma-separated string of question IDs
                        def objIds = questions.collect { it.id }.join(',')

                        // Calculate section marks
                        def sectionMarks = (section.marksPerQuestion as Integer) * (section.totalQuestions as Integer)
                        totalMarks += sectionMarks

                        // Create question paper detail record
                        def quesPaperDtl = new com.wonderslate.qp.QuesPaperDtl(
                            quesPaperId: quesPaperMst.id,
                            sectionHeading: section.sectionHeader,
                            totalMarks: sectionMarks,
                            noOfQuestions: section.totalQuestions as Integer,
                            objIds: objIds,
                            questionType: section.questionType
                        )

                        if (!quesPaperDtl.save(flush: true)) {
                            println("Error saving QuesPaperDtl: ${quesPaperDtl.errors}")
                        }
                    }
                }
            }

            // Update total marks in master record
            quesPaperMst.totalMarks = totalMarks
            quesPaperMst.save(flush: true)

            return [success: true, questionPaperId: quesPaperMst.id]
        } catch (Exception e) {
            println("Error generating question paper: ${e.message}")
            return [success: false, message: e.message]
        }
    }

    def getQuestionPaperDetails(Long questionPaperId) {
        try {
            def quesPaperMst = com.wonderslate.qp.QuesPaperMst.get(questionPaperId)
            if (!quesPaperMst) {
                return [success: false, message: "Question paper not found"]
            }

            def quesPaperDtls = com.wonderslate.qp.QuesPaperDtl.findAllByQuesPaperId(questionPaperId)

            def sections = []
            quesPaperDtls.each { dtl ->
                if (dtl.objIds) {
                    def objIdsList = dtl.objIds.split(',').collect { it.trim() as Long }
                    def questions = getQuestionsByIds(objIdsList)

                    sections << [
                        sectionHeading: dtl.sectionHeading,
                        totalMarks: dtl.totalMarks,
                        noOfQuestions: dtl.noOfQuestions,
                        questionType: dtl.questionType,
                        questions: questions
                    ]
                }
            }

            return [
                success: true,
                questionPaper: quesPaperMst,
                sections: sections
            ]
        } catch (Exception e) {
            println("Error getting question paper details: ${e.message}")
            return [success: false, message: e.message]
        }
    }

    def getQuestionsByIds(List<Long> questionIds) {
        try {
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new SafeSql(dataSource)

            String idsStr = questionIds.join(',')

            String sql = "SELECT om.id, om.question, om.answer, om.q_type qType, " +
                        "om.option1, om.option2, om.option3, om.option4, om.option5, " +
                        "om.answer1, om.answer2, om.answer3, om.answer4, om.answer5, " +
                        "rd.chapter_id chapterId " +
                        "FROM objective_mst om " +
                        "LEFT JOIN resource_dtl rd ON om.quiz_id = rd.res_link " +
                        "WHERE om.id IN (" + idsStr + ") " +
                        "ORDER BY FIELD(om.id, " + idsStr + ")"

            def results = sql1.rows(sql)
            return results
        } catch (Exception e) {
            println("Error getting questions by IDs: ${e.message}")
            return []
        }
    }

    def getChapterIdsFromQuestionPaper(Long questionPaperId) {
        try {
            def quesPaperDtls = com.wonderslate.qp.QuesPaperDtl.findAllByQuesPaperId(questionPaperId)
            def chapterIds = [] as Set

            quesPaperDtls.each { dtl ->
                if (dtl.objIds) {
                    def objIdsList = dtl.objIds.split(',').collect { it.trim() as Long }
                    def questions = getQuestionsByIds(objIdsList)
                    questions.each { question ->
                        if (question.chapterId) {
                            chapterIds.add(question.chapterId as Long)
                        }
                    }
                }
            }

            return chapterIds.toList()
        } catch (Exception e) {
            println("Error getting chapter IDs from question paper: ${e.message}")
            return []
        }
    }

    def getQuestionPapersList(Long bookId) {
        try {
            def questionPapers = com.wonderslate.qp.QuesPaperMst.findAllByBookId(bookId as Integer, [sort: 'dateCreated', order: 'desc'])
            return questionPapers
        } catch (Exception e) {
            println("Error getting question papers list: ${e.message}")
            return []
        }
    }

    def hasQuestionsInBook(Long bookId) {
        try {
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new SafeSql(dataSource)

            // Get all chapters for this book
            String chapterQuery = "SELECT id FROM chapters_mst WHERE book_id = " + bookId
            def chapters = sql1.rows(chapterQuery)

            if (chapters.isEmpty()) {
                return false
            }

            def chapterIds = chapters.collect { it.id }.join(',')

            // Check if any chapter has questions
            String questionQuery = "SELECT COUNT(*) as count " +
                                 "FROM resource_dtl rd " +
                                 "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                                 "WHERE rd.chapter_id IN (" + chapterIds + ") " +
                                 "AND rd.resource_name IN ('QuestionBank QnA', 'QuestionBank MCQs') " +
                                 "AND rd.res_type IN ('QA', 'Multiple Choice Questions') " +
                                 "LIMIT 1"

            def result = sql1.rows(questionQuery)
            return result && result[0] && result[0].count > 0
        } catch (Exception e) {
            println("Error checking if book has questions: ${e.message}")
            return false
        }
    }

    def deleteQuestion(Long questionId) {
        try {
            // Find the question in ObjectiveMst table
            ObjectiveMst question = ObjectiveMst.findById(questionId)

            if (!question) {
                return [success: false, message: "Question not found with ID: ${questionId}"]
            }

            // Delete the question
            question.delete(flush: true)

            log.info("Successfully deleted question with ID: ${questionId}")
            return [success: true, message: "Question deleted successfully"]

        } catch (Exception e) {
            log.error("Error deleting question with ID ${questionId}: ${e.message}", e)
            return [success: false, message: "Failed to delete question: ${e.message}"]
        }
    }
}
