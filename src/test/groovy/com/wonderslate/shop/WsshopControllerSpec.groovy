package com.wonderslate.shop

import grails.testing.web.controllers.ControllerUnitTest
import spock.lang.Specification
import grails.converters.JSON

class WsshopControllerSpec extends Specification implements ControllerUnitTest<WsshopController> {

    def setup() {
        // Mock the utilService
        controller.utilService = Mock()
        controller.utilService.getSiteId(_, _) >> 1
    }

    void "test validateCouponCode with empty coupon code"() {
        when:
        params.couponCode = ""
        controller.validateCouponCode()

        then:
        response.json.status == "ERROR"
        response.json.message == "Coupon code is required"
    }

    void "test validateCouponCode with null coupon code"() {
        when:
        params.couponCode = null
        controller.validateCouponCode()

        then:
        response.json.status == "ERROR"
        response.json.message == "Coupon code is required"
    }

    void "test validateCouponCode with whitespace only coupon code"() {
        when:
        params.couponCode = "   "
        controller.validateCouponCode()

        then:
        response.json.status == "ERROR"
        response.json.message == "Coupon code is required"
    }

    void "test validateCouponCode handles case insensitive matching"() {
        given:
        // This test would require mocking the domain classes
        // For now, we'll test the basic structure
        
        when:
        params.couponCode = "TEST123"
        controller.validateCouponCode()

        then:
        // Since we don't have the domain classes mocked, 
        // this will likely return an error, but we can verify the method executes
        response.json.status != null
    }

    void "test validateCouponCode handles spaces in coupon code"() {
        when:
        params.couponCode = " TEST 123 "
        controller.validateCouponCode()

        then:
        // The method should normalize the coupon code by removing spaces
        response.json.status != null
    }
}
