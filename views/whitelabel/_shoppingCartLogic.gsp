<%
    String serverURL = request.getScheme()+"://"+request.getServerName()+
            ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                    "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                    request.getServerPort())
%>
<%if("true".equals(session["prepjoySite"])){%>
<g:render template="/prepjoy/prepjoy-loader"></g:render>
<% } %>
<script>
    var bookId;
    var bookPrice;
    var bookListPrice;
    var discountId;
    var discountPrice = 0;
    var discountPercent = 0;
    var payablePrice = 0;
    var subTotal = 0;
    var totalDiscountPrice = 0;
    var totalPayablePrice = 0;
    var cartItems;
    var cartBooksCount;
    var rupeeSymbol = '&#x20b9';
    var coverImage;
    var bookImageSrc;
    var bookType;
    var forSession = [];
    var forSessionObj = {};
    var siteName = "";
    var hasPrintBooks=false;
    var baseBookType="";
    var printAddressHolderId = null;

    <%if("true".equals(session["commonWhiteLabel"])){%>
    siteName = "${session['siteName']}";
    <%}else{%>
    siteName = "${session['entryController']}";
    <%}%>

    var couponCode = "";
    var prepjoySite = "${session['prepjoySite']}";
    var globalCouponCode = "";
    var globalDiscountId = "";
    var globalDiscountValue = 0;
    var globalDiscountPercentage = 0;
    function getCartItems() {
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        <g:remoteFunction controller="wsshop" action="getUsersCartDetails" onSuccess='showCartBooks(data);'/>
    }


    function showCartBooks(data) {
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        cartItems = data.userCartDetails;
        cartBooksCount = data.count;
        forSession = [];
        hasPrintBooks = false;
        var htmlStr='';

        if(cartItems != null && cartItems != "null" && cartItems != "") {
            for(var books of cartItems ) {
                if("printbook"==books.bookType||"combo"==books.bookType||"comboGPT"==books.bookType){
                    hasPrintBooks = true;
                }
                htmlStr +="<div>" +
                    displayCartItems(books);
                htmlStr +="</div>";
            }
            document.getElementById("cartItems").innerHTML = htmlStr;
            document.getElementById("cartBooksCount").innerHTML = cartBooksCount;
            $("#navbarCartCount").text(cartBooksCount);
            $('#displayCartItems').removeClass('d-none').addClass('d-flex');
            $('#cartSummaryCard').removeClass('d-none');
            $('#showEmptyCart').addClass('d-none');
        } else {
            $('#showEmptyCart').removeClass('d-none');
        }
    }

    //MAIN CONTROLLER FOR CART ITEMS
    function displayCartItems(books) {
        var bookTitle = books.title || books.bookTitle;
        var publisherName = books.publisherName;
        bookId = books.id || books.bookId;
        bookPrice = books.bookPrice;
        bookListPrice = books.listPrice;
        discountPrice = books.discountValue;
        discountId = books.discountId;
        couponCode = books.couponCode;
        coverImage = books.bookImg;
        bookType = books.bookType;
        baseBookType = books.baseBookType;
        if(coverImage.startsWith('http'))
            bookImageSrc = coverImage;
        else  bookImageSrc = "/funlearn/showProfileImage?id="+bookId+"&fileName="+coverImage+"&type=books&imgType=webp";

        if(coverImage != ''){
            var coverImageStr ="<img class='animate__animated animate__fadeIn' src='" + bookImageSrc + "' alt='"+coverImage+"'>";
        } else if(coverImage=="" && bookType=="recharge"){
            let txt = ""
            let bg = ""
            if(bookPrice==20){
                txt = "STARTER"
                bg  = "#3b82f6"
            }else if(bookPrice==30){
                txt = "FIGHTER"
                bg  = "#a78bfa"
            }else if(bookPrice==50){
                txt = "CHAMPION"
                bg  = "#fb7185"
            }
            var coverImageStr = "<div class='rechargeCartCover' style='background: "+bg+";color: #fff; !important;'>"+txt+"</div>"
        }else{
            var coverImageStr = "<img class='animate__animated animate__fadeIn' src='/assets/booksmojo/img_cover_placeholder.png' alt='Book cover image'>";
        }

        if(discountPrice && discountId) {
            var discountCalculation = (discountPrice/bookPrice)*100;
            discountPercent = Math.round(discountCalculation);
        }

        forSessionObj = {
            bookId:bookId,
            bookPrice:bookPrice,
            payablePrice:bookPrice,
            couponCode:couponCode,
            discountAmount:"",
            discountId:"",
            discountApplied:"false",
            bookType:bookType,
            subsDuration:books.subsDuration,
            subsStartingBookId:books.subsStartingBookId,
            subscriptionId:books.subscriptionId,
            bookListPrice:books.listPrice,
            baseBookType:books.baseBookType

        };
        forSession.push(forSessionObj);

        updatePrices();

        var template = cartBookUILayer(bookId,bookTitle,coverImageStr,publisherName,bookPrice,discountPrice,discountId,discountPercent,couponCode,bookType,books.subsDuration,books.subsStartingBookId,books.listPrice,books.baseBookType);
        return template;
    }

    //MAIN UI TEMPLATE
    function cartBookUILayer(bookId,bookTitle,coverImageStr,publisherName,bookPrice,discountPrice,discountId,discountPercent,couponCode,bookType,subsDuration,subsStartingBookId,bookListPrice,baseBookType){
        var bookUrl=         "<a href='/" + replaceAll(bookTitle,' ','-') + "/ebook-details?siteName=" + siteName + "&bookId=" + bookId + "&publisher=" + replaceAll(publisherName,' ','-') + "&preview=true' target='_blank'>\n";
        if("subscription"==bookType)
            bookUrl=         "<a href='/" + replaceAll(bookTitle,' ','-') + "/ebook-details?siteName=" + siteName + "&bookId=" + bookId + "&subsDuration="+subsDuration+"&subsStartingBookId="+subsStartingBookId+"&publisher=" + replaceAll(publisherName,' ','-') + "&preview=true' target='_blank'>\n";

        var bookUItemplate= "<div id='cartItemBook"+bookId+"' class='card border-0 mb-3 mb-md-2 p-3'>" +
            "<div class='d-flex align-items-start'>\n" +
                "<div class='book_image'>\n" +
        bookUrl            + coverImageStr +
                    "</a>" +
                "</div>";
        bookUItemplate +="<div class='book_desc col'>\n" +
                "<div class='book_title'>\n" +
                    "<h5>" +
            bookUrl            + bookTitle +
                        "</a>" +
                    "</h5>\n";

        if("recharge"!=bookType){
        bookUItemplate += "<p class='book_publisher'>By ";
        <%if("true".equals(session["commonWhiteLabel"])){%>
        bookUItemplate += "<a class='font-weight-normal' href='/sp/"+ siteName+"/store?linkSource=shoppingCart&publisher="+ replaceAll(publisherName,' ','-') + "' target='_blank'>\n"+ publisherName + "</a>";
        <%} else if("true".equals(session["prepjoySite"])){%>
        bookUItemplate += "<a class='font-weight-normal' href='/"+ siteName+"/eBooks?linkSource=shoppingCart&publisher="+ replaceAll(publisherName,' ','-') + "' target='_blank'>\n"+ publisherName + "</a>";
        <%} else {%>
        bookUItemplate += "<a class='font-weight-normal' href='/"+ siteName+"/store?linkSource=shoppingCart&publisher="+ replaceAll(publisherName,' ','-') + "' target='_blank'>\n"+ publisherName + "</a>";
        <%}%>
        bookUItemplate += "</p>\n";
        }

        if("eBook"==bookType){
            if("ebookwithai"==baseBookType){
                bookUItemplate += "<p class='book_publisher'>eBook (with AI Doubts Solver)</p>\n" ;
            }else {
                bookUItemplate += "<p class='book_publisher'>eBook</p>\n";
            }
        }
        else if("testSeries"==bookType){

            bookUItemplate += "<p class='book_publisher'>Online Test Series</p>\n" ;
        }
        else if("upgrade"==bookType){

            bookUItemplate += "<p class='book_publisher'>Online Test Series</p>\n" ;
        }
        else if("subscription"==bookType){
            bookUItemplate += "<p class='book_publisher'>Subscription</p>\n" ;
        }
        else if("printbook"==bookType){
            bookUItemplate += "<p class='book_publisher'>Paperback</p>\n" ;
        }
        else if("combo"==bookType){
            bookUItemplate += "<p class='book_publisher'>Paperback + eBook</p>\n" ;
        }else if("recharge"==bookType){
            bookUItemplate += "<p class='book_publisher'>Doubts Recharge</p>\n" ;
        }else if("comboGPT"==bookType){
            bookUItemplate += "<p class='book_publisher'>Paperback + Book GPT</p>\n" ;
        }
        else if("bookGPT"==bookType){
            bookUItemplate += "<p class='book_publisher'>Book GPT</p>\n" ;
        }
        else if("ibookgptpro"==bookType){
            bookUItemplate += "<p class='book_publisher'>iBookGPT Pro</p>\n" ;
        }
        else if("printGPTUpgrade"==bookType||"ebookGPTUpgrade"==bookType){
            bookUItemplate += "<p class='book_publisher'>Upgrade to GPT</p>\n" ;
        }
        bookUItemplate +=        "</div>\n"
        if(bookListPrice) {
            bookUItemplate +=  "<p class='book_price mrp'>\n" +
            "<del id='listPrice" + bookId + "' class='list_price'>" + rupeeSymbol + " " + bookListPrice + "</del>" +
            "</p>\n"
        }
         bookUItemplate +=     "<p class='book_price'>\n" +
                    "<span id='offerPrice"+bookId+"' class='offer_price'>"+rupeeSymbol+" "+bookPrice+"</span>";

        bookUItemplate +="</p>\n" +
            "</div>\n";
        bookUItemplate +="<div class='delete_item'>" +
                    "<a href='javascript:removeBookFromCart("+bookId+")' class='d-flex align-items-center'><i class='material-icons-round'>delete</i></a>" +
                "</div>" +
            "</div>";
        return bookUItemplate;
    }


    function applyDiscount(bookId,bookPrice,discountPrice,discountId,couponCode){

        // Book price calculation
        var calculatePrice = Number(bookPrice - discountPrice);
        payablePrice = Math.round((calculatePrice + Number.EPSILON) * 100) / 100;
        $('#listPrice'+bookId).removeClass('d-none').html(rupeeSymbol+" "+bookPrice);
        $('#offerPrice'+bookId).html(rupeeSymbol+" "+payablePrice);

        for(var i = 0; i < forSession.length; i++){
            if(forSession[i].bookId === bookId){
                forSession[i].bookPrice = bookPrice;
                forSession[i].payablePrice = payablePrice;
                forSession[i].couponCode = couponCode;
                forSession[i].discountAmount = discountPrice;
                forSession[i].discountId = discountId;
                forSession[i].discountApplied = "true";
            }
        }

        // Show discount applied text
        $('#applyDiscountBtn'+bookId).remove();
        $('#discountAppliedText'+bookId).removeClass('d-none').addClass('animate__bounceIn');

        updatePrices();
    }

    function updatePrices() {
        var cartSubtotal = 0;
        var discPrice = 0;
        var totalPayable = 0;
        if(forSession != null) {
            if(forSession.length>0) {

                for(var i = 0; i < forSession.length; i++) {
                    if(forSession[i].bookPrice != "") {
                        cartSubtotal += forSession[i].bookPrice;
                    }
                    if(forSession[i].discountAmount != "") {
                        discPrice += forSession[i].discountAmount;
                    }
                    if(forSession[i].payablePrice != "") {
                        totalPayable += forSession[i].payablePrice;
                    }
                }

                subTotal = cartSubtotal;
                totalDiscountPrice = discPrice;
                totalPayablePrice = Math.round((totalPayable + Number.EPSILON) * 100) / 100;

                $('#subTotal').html(rupeeSymbol+" "+subTotal);
                if(totalDiscountPrice === 0) {
                    $('#discountPriceFinal').parent().addClass('d-none');
                    $('#discountDiv').addClass('d-none');
                } else {
                    $("#displayLabel").show();
                    $("#displayValue").show();
                    $("#discountTotal").show();
                    $('#discountPriceFinal').html("You saved <span class='saved_price'>"+rupeeSymbol+" "+totalDiscountPrice+"</span>").parent().removeClass('d-none');
                }
                $('#discountTotal').html(rupeeSymbol+" "+totalDiscountPrice);
                $('#payableTotal').html(rupeeSymbol+" "+totalPayablePrice);
                $('.mobile_total').html("<span>Total</span><br> "+rupeeSymbol+" "+totalPayablePrice);

                $('#displayCartItems').removeClass('d-none').addClass('d-flex');
                $('#cartSummaryCard').removeClass('d-none');
            } else {
                $('#payableTotal,#discountTotal,#subTotal').html(rupeeSymbol+" 0");
                $('#displayCartItems').removeClass('d-flex').addClass('d-none');
                $('#cartSummaryCard').addClass('d-none');
                $('#showEmptyCart').removeClass('d-none');
            }
        }
    }

    // Show or hide order summary in mobile web
    function openOrderSummary() {
        $('.mobile_view_summary').toggleClass('opened');
        $('.order_summary').toggleClass('showing');
        if ($('.mobile_view_summary').hasClass("opened")) {
            $('.mobile_view_summary a').html('Hide<br>Summary');
        } else {
            $('.mobile_view_summary a').html('View<br>Summary');
        }
    }

    // Close order summary
    $('#closeOrderSummary').on('click', function () {
        $('.mobile_view_summary').removeClass('opened');
        $('.order_summary').removeClass('showing');
        $('.mobile_view_summary a').html('View<br>Summary');
    });

    // Remove book item from cart
    function removeBookFromCart(bookId) {
        $('#deleteCartBookModal').modal('show');
        $('#deleteCartBookBtn').attr('onclick','deleteBookFromCart('+bookId+')');
    }

    function deleteBookFromCart(bookId) {
        $('#deleteCartBookModal').modal('hide');
        $("#billingShipping").hide();
        $('#cartItemBook'+bookId).fadeOut( "slow", function() {
            $('#cartItemBook'+bookId).remove();
        });
        <g:remoteFunction controller="wsshop" action="deleteBookFromCart" params="'bookId='+bookId" onSuccess='cartItemDeleted(data);'/>
        removeByAttr(forSession,'bookId',bookId);

        var dd = Number($('#cartBooksCount').text()) - 1;
        $('#cartBooksCount,#navbarCartCount').text(dd);
    }

    function cartItemDeleted(data) {
        if(data.status=='OK') {
            if (!prepjoySite){
                $('.loading-icon').removeClass('hidden');
            }else{
                $('#loading').show();
            }
            location.reload();
        }
    }

    // Deleting items in session array
    var removeByAttr = function(arr, attr, value){
        var i = arr.length;
        while(i--){
            if( arr[i]
                && arr[i].hasOwnProperty(attr)
                && arr[i][attr] === value){
                arr.splice(i,1);
            }
        }
        return arr;
    }

    // Show/Hide coupon code section
    function toggleCouponSection() {
        $('#couponSection').toggle();
        if ($('#couponSection').is(':visible')) {
            $('#couponToggleText').text('Hide Coupon Code');
            $('#couponCodeInput').focus();
        } else {
            $('#couponToggleText').text('Have Coupon Code?');
            $('#couponCodeInput').val('');
            $('#couponMessage').text('').removeClass('text-success text-danger');
        }
    }

    // Validate and apply coupon code
    function applyCouponCode() {
        var couponCodeInput = $('#couponCodeInput').val().trim();

        if (!couponCodeInput) {
            $('#couponMessage').text('Please enter a coupon code').removeClass('text-success').addClass('text-danger');
            return;
        }

        // Disable apply button and show loading
        $('#applyCouponBtn').prop('disabled', true).text('Applying...');
        $('#couponMessage').text('Validating coupon code...').removeClass('text-success text-danger');

        $.ajax({
            type: 'POST',
            url: '/wsshop/validateCouponCode',
            data: { couponCode: couponCodeInput },
            dataType: 'json',
            success: function(data) {
                if (data.status === 'OK') {
                    globalCouponCode = data.couponCode;
                    globalDiscountId = data.discountId;
                    globalDiscountValue = data.discountValue || 0;
                    globalDiscountPercentage = data.discountPercentage || 0;

                    $('#couponMessage').text('Coupon code applied successfully!').removeClass('text-danger').addClass('text-success');
                    $('#applyCouponBtn').text('Applied').prop('disabled', true);
                    $('#couponCodeInput').prop('disabled', true);

                    // Apply discount to all cart items
                    applyGlobalDiscount();
                } else {
                    $('#couponMessage').text(data.message || 'Invalid coupon code').removeClass('text-success').addClass('text-danger');
                    $('#applyCouponBtn').prop('disabled', false).text('Apply');
                }
            },
            error: function() {
                $('#couponMessage').text('Error validating coupon code. Please try again.').removeClass('text-success').addClass('text-danger');
                $('#applyCouponBtn').prop('disabled', false).text('Apply');
            }
        });
    }

    // Apply global discount to all cart items
    function applyGlobalDiscount() {
        if (!globalCouponCode || (!globalDiscountValue && !globalDiscountPercentage)) {
            return;
        }

        for (var i = 0; i < forSession.length; i++) {
            var item = forSession[i];
            var discountAmount = 0;

            if (globalDiscountValue > 0) {
                discountAmount = globalDiscountValue;
            } else if (globalDiscountPercentage > 0) {
                discountAmount = (item.bookPrice * globalDiscountPercentage) / 100;
            }

            // Ensure discount doesn't exceed book price
            if (discountAmount > item.bookPrice) {
                discountAmount = item.bookPrice;
            }

            applyDiscount(item.bookId, item.bookPrice, discountAmount, globalDiscountId, globalCouponCode);
        }
    }

    function proceedToBuy(){
        if(hasPrintBooks){
            $("#billingShipping").show(500);
            document.getElementById("proceedToBuyButton").href="javascript:submitShippingBilling();";
            document.getElementById("proceedToBuyButton").textContent = 'Confirm Shipping Address';
            document.querySelector('.shopping_cart').style = 'min-height:auto';
            document.querySelector('.shopping_cart').classList.add('printBookMobileCheckout');
            document.getElementById("billingShippingForm").scrollIntoView({ behavior: 'smooth' });
            $("#billingShipping").addClass('printBookMobilePB')
        }
        else {
            if (userLoggedIn) {
                proceedToBuyContinue();
            } else {
                if (!prepjoySite) {
                    if ('${session['wileySite']}' !='true'){
                        signupWithFunction('moveCartItems', "You're almost done – register to complete your order!");
                    }else{
                        loginOpenWithFunction('moveCartItems', "You're almost done – register to complete your order!");
                    }

                } else {
                    signupWithFunctionPrepjoy('moveCartItems');
                }
            }
        }
    }

  function payNow(){
      if (userLoggedIn) {
          proceedToBuyContinue();
      } else {
          if (!prepjoySite) {
              signupWithFunction('moveCartItems', "You're almost done- just one more step!");
          } else {
              signupWithFunctionPrepjoy('moveCartItems');
          }
      }
  }
   function shippingDeliveryUpdated(data){
       $('#shippingTotal').html(rupeeSymbol+" "+data.shippingCharges);
       totalPayablePrice = totalPayablePrice+Number(data.shippingCharges);
       printAddressHolderId = data.addressHolderId ? data.addressHolderId : null;
       $('#payableTotal').html(rupeeSymbol+" "+totalPayablePrice);
       document.getElementById("payableTotal").style.color = "green";
       document.getElementById("payableTotalLabel").style.color = "green";
       document.getElementById("proceedToBuyButton").href="javascript:payNow();";
       document.getElementById("proceedToBuyButton").textContent = 'Pay Now';
       enableDisableEditField('disable');
   }


    function moveCartItems(){
       <g:remoteFunction controller="wsshop" action="moveCartItems"  onSuccess="cartItemsToMoved(data);"  />
    }

    function cartItemsToMoved(data){
        // here is the logic delivery charges.
        proceedToBuyContinue();
    }
    function proceedToBuyContinue() {
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        var finalObj = {};
        finalObj = {
            cartData:forSession,
            totalPayableAmount: totalPayablePrice,
        };

        if(printAddressHolderId!=null){
            finalObj.addressHolderId = printAddressHolderId;
        }

        $.ajax({
            type: 'POST',
            url: '/wsshop/addCartBooksToBuy',
            dataType: 'json',
            contentType: 'application/json',
            data: JSON.stringify(finalObj),
            success: function (data,status,xhr) {
                if (data.status == "OK") {
                    if (!prepjoySite){
                        $('.loading-icon').addClass('hidden');
                    }else{
                        $('#loading').hide();
                    }
                    buyBookFromCart(data.shoppingCartId,totalPayablePrice);
                } else {
                    if(data.outOfStock=="true"){
                        alert(data.outOfStockBookTitle+ ' got sold out. Sorry!');
                        deleteBookFromCart(data.outOfStockBookId);
                    }
                     else {
                         alert('Something went wrong!.Please try again.');
                    }
                    if (!prepjoySite){
                        $('.loading-icon').addClass('hidden');
                    }else{
                        $('#loading').hide();
                    }
                }
            }
        });

    }

    var finalCartId;
    var finalTotalPrice;
    function getUserDetails(){
        <g:remoteFunction controller="usermanagement" action="getUserDetailsForCart"  onSuccess='receivedUserDetails(data);' />
    }

    function receivedUserDetails(data){
        openRazorPayFromCart(finalCartId,finalTotalPrice,data.username,data.name,data.mobile,data.email);
    }
    function buyBookFromCart(cartId,totalPrice) {
        finalCartId = cartId;
        finalTotalPrice = totalPrice
        getUserDetails()
    }

    $('.continue_shop_btn').on('click', function () {
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
    });

    if (!prepjoySite){
        $('.loading-icon').removeClass('hidden');
    }else{
        $('#loading').show();
    }
    window.addEventListener( "pageshow", function () {
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
    }, false);

    getCartItems();

    // Add Enter key functionality to coupon input
    $(document).on('keypress', '#couponCodeInput', function(e) {
        if (e.which === 13) { // Enter key
            e.preventDefault();
            applyCouponCode();
        }
    });

    var fromEmail = "${params.emailCampaign}";
    var encryptedLoginId = "${params.username}";
    if(fromEmail) {
        <%if(session['userdetails']==null){%>
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
            <g:remoteFunction controller="log" action="login"  onSuccess="autoLoginDone(data);" params="'source=web&autoLogin=true&loginId='+encryptedLoginId" />
        <%}%>
    }

    function autoLoginDone(data) {
        if ("ok" == data.status || "OK" == data.status) {
            if (!prepjoySite){
                $('.loading-icon').removeClass('hidden');
            }else{
                $('#loading').show();
            }
            location.reload();
        }
    }

    document.addEventListener("visibilitychange", function() {
        if (document.hidden) {
        } else {
            // this functionality is really not needed. We will revisit if it is really needed.
        //    getCartItems();
        }
    });
</script>
