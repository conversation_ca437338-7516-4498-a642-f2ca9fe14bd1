@import "../wstemplate/theme/colors.less";
@import "../wstemplate/theme/fonts.less";
@import "../wstemplate/theme/responsive.less";

// Shopping cart page style for all sites
.shopping_cart {
  @media @extraSmallDevices, @smallDevices {
    padding-bottom: 150px;
  }
  h3 {
    margin-bottom: 20px;
    font-weight: @medium;
    @media @extraSmallDevices, @smallDevices {
      font-size: 1.5rem;
    }
    small {
      font-weight: @medium;
      //opacity: 0.8;
      font-size: 18px;
    }
  }
  .cart_items {
    .card {
      border-radius: 10px;
      box-shadow: 0 4px 10px @gray-light-shadow;
      min-height: 150px;
      transition: all 0.2s linear;
      -webkit-transition: all 0.2s linear;
      @media @extraSmallDevices, @smallDevices {
        min-height: auto;
      }
      &:hover {
        box-shadow: 0 2px 4px @gray-dark-shadow;
        .book_image {
          a img {
            box-shadow: 0 2px 4px @gray-dark-shadow;
          }
        }
        .book_desc {
          h5 a {
            text-decoration: underline;
          }
        }
      }
    }
    .book_image {
      a {
        display: block;
        transition: all 0.2s linear;
        -webkit-transition: all 0.2s linear;
        &:hover {
          opacity: 0.8;
        }
      }
      img {
        width: 90px;
        height: 120px;
        border-radius: 5px;
        transition: all 0.2s linear;
        -webkit-transition: all 0.2s linear;
        @media @extraSmallDevices, @smallDevices {
          width: 75px;
          height: 100px;
        }
      }
    }
    .book_desc {
      @media @extraSmallDevices, @smallDevices {
        padding-right: 0;
        padding-left: 12px;
      }
      .book_title {
        min-height: 75px;
        @media @extraSmallDevices, @smallDevices {
          min-height: 70px;
        }
      }
      h5 {
        color: @dark-gray;
        font-size: 16px;
        font-weight: normal;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        @media @extraSmallDevices, @smallDevices {
          font-size: 15px;
          //padding-top: 7px;
          -webkit-line-clamp: 3;
        }
        a {
          color: @dark-gray;
          transition: all 0.2s linear;
          -webkit-transition: all 0.2s linear;
          &:hover {
            opacity: 0.8;
          }
        }
      }
      .book_publisher {
        color: @light-gray;
        margin-bottom: 0.25rem;
        line-height: normal;
        @media @extraSmallDevices, @smallDevices {
          font-size: 13px;
        }
      }
      .book_price {
        display: flex;
        margin: 3px 0 0;
        position: relative;
        z-index: 11;
        flex-wrap: wrap;
        //width: 70%;
        //@media @extraSmallDevices, @smallDevices {
        //  width: 90%;
        //}
        @media (max-width: 350px) {
          display: grid;
        }
        &.mrp {
          margin: 0;
          line-height: normal;
          width: 100px;
        }
        .offer_price {
          font-size: 18px;
          font-weight: @medium;
          min-width: 55px;
          font-family: @secondary-font;
          margin-right: 0.75rem;
          @media @extraSmallDevices, @smallDevices {
            font-size: 16px;
            min-width: 50px;
            margin-bottom: 5px;
          }
        }
        .list_price {
          font-size: 15px;
          //margin-left: 0.25rem;
          color: @dark-red;
          font-family: @secondary-font;
          @media @extraSmallDevices, @smallDevices {
            font-size: 14px;
          }
        }
        .book_discount_btn {
          height: auto;
          border-radius: 3px;
          border: 1px dashed #d4d4d4;
          //background-color: @green;
          line-height: normal;
          color: @gray;
          box-shadow: 0 2px 4px @gray-light-shadow;
          font-weight: @medium;
          white-space: normal;
          font-size: 13px;
          padding: 4px 20px;
        }
        .discount_applied {
          color: @green;
          font-weight: @medium;
          min-height: 25px;
          white-space: normal;
        }
      }
    }
    .delete_item {
      @media @extraSmallDevices, @smallDevices {
        position: absolute;
        z-index: 1;
        right: 0;
        top: 0;
      }
      a {
        font-size: 11px;
        color: @dark-red;
        line-height: normal;
        @media @extraSmallDevices, @smallDevices {
          padding: 5px;
        }
        &:hover {
          color: @dark-red;
          text-decoration: none;
        }
        i {
          font-size: 20px;
          @media @extraSmallDevices, @smallDevices {
            font-size: 18px;
          }
        }
      }
    }
  }
  .cart_checkout {
    border-radius: 10px;
    box-shadow: 0 4px 10px @gray-light-shadow;
    position: sticky;
    top: 0;
    transition: all 0.5s linear;
    @media @extraSmallDevices, @smallDevices {
      position: relative;
      top: 0 !important;
      padding: 0 !important;
      box-shadow: none;
    }
    h5 {
      border-bottom: 1px dashed lighten(@light-gray,30%);
      @media @extraSmallDevices, @smallDevices {
        font-size: 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        i {
          font-size: 16px;
        }
      }
    }
    .text-right {
      font-weight: @semi-bold;
      font-family: @secondary-font;
    }
    .total_discount_price {
      color: @green;
      font-weight: @medium;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 3px;
      @media @extraSmallDevices, @smallDevices {
        position: absolute;
        left: 0;
        right: 0;
        margin-bottom: -5px;
      }
      #totalDiscountPercent {
        width: 45px;
        height: 45px;
        position: relative;
        z-index: -1;
        margin: -40px -5px 0;
        @media @extraSmallDevices, @smallDevices {
          width: 40px;
          height: 40px;
        }
      }
      .saved_price {
        font-family: @secondary-font;
      }
    }
    .order_summary {
      @media @extraSmallDevices, @smallDevices {
        position: fixed;
        width: 100%;
        padding: 14px;
        bottom: -100%;
        left: 0;
        right: 0;
        z-index: 9991;
        background: #f9f9f9;
        border-radius: 7px 7px 0 0;
        border-bottom: 1px solid #f5f5f5;
        box-shadow: 0 -0.5rem 1rem @gray-light-shadow;
        transition: all 0.3s linear;
        &.showing {
          bottom: 130px;
        }
      }
      p {
        margin-bottom: 1rem;
        @media @extraSmallDevices, @smallDevices {
          margin-bottom: 0.5rem;
        }
      }
    }
    .cart_buttons {
      @media @extraSmallDevices, @smallDevices {
        position: fixed;
        bottom: 0;
        z-index: 9992;
        background-color: @white;
        width: 100%;
        height: 130px;
        left: 0;
        right: 0;
        padding: 0 14px 10px;
        box-shadow: 0 -0.5rem 1rem @gray-light-shadow;
        border-top: 1px solid #DDD;
      }
      a {
        width: 100%;
        border: 1px solid transparent;
        border-radius: 7px;
        box-shadow: 0 2px 4px @gray-light-shadow;
        font-weight: @medium;
        &.proceed_btn {
          border: none;
          background-color: @orange;
          color: @white;
          position: relative;
          overflow: hidden;
          font-weight: @semi-bold;
          font-size: 1.25rem;
          @media @extraSmallDevices, @smallDevices {
            height: 40px;
            line-height: normal;
          }
          &:after {
            content: '';
            position: absolute;
            top: -10px;
            left: -50%;
            z-index: 10;
            display: block;
            width: 30px;
            height: 100px;
            opacity: 0.7;
            background: -webkit-linear-gradient(left, rgba(255,255,255,0) 0%, rgba(255,255,255,.3) 100%);
            background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,.3) 100%);
            -webkit-transform: skewX(-40deg);
            transform: skewX(-40deg);
            -webkit-animation: shine 3s infinite;
            animation: shine 3s infinite;
            filter: blur(5px);
          }
        }
        &.continue_shop_btn {
          color: @gray;
        }
      }
    }
    .mobile_summary {
      @media @extraSmallDevices, @smallDevices {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
    .mobile_total, .mobile_view_summary {
      //line-height: normal;
      margin-bottom: 0;
      text-align: center;
      padding: 3px 0;
      position: relative;
      z-index: 1;
      font-weight: @medium;
      display: none;
      @media @extraSmallDevices, @smallDevices {
        display: block;
      }
      span {
        font-weight: normal;
      }
    }
    .mobile_total {
      font-size: 12px;
      font-family: @secondary-font;
      line-height: normal;
    }
    .mobile_view_summary {
      a.open_order_summary {
        font-size: 12px;
        display: flex;
        text-decoration: none;
        box-shadow: none;
        font-weight: @regular;
        align-items: center;
        line-height: 1;
        color: @light-gray;
      }
    }
  }

  #discountAnimation {
    position: fixed;
    bottom: 0;
    left: 0;
    top: 0;
    right: 0;
    width: 70%;
    height: 70%;
    margin: auto;
    display: none;
    z-index: 12;
    @media @extraSmallDevices, @smallDevices {
      position: fixed;
      bottom: -100px;
      z-index: 9993;
      width: 100%;
      height: 100%;
    }
  }

  .order_information {
    ul {
      padding: 10px 0 0;
      list-style: none;
      margin-bottom: 0;
      li {
        line-height: normal;
        padding-bottom: 15px;
        color: @gray;
        display: flex;
        align-items: center;
        span {
          display: inline-block;
          &:nth-child(1) {
            width: 180px;
            @media @extraSmallDevices, @smallDevices {
              width: 150px;
            }
          }
          &:nth-child(2) {
            width: 50px;
            @media @extraSmallDevices, @smallDevices {
              width: 20px;
            }
          }
          &:nth-child(3) {
            //width: 150px;
          }
        }
      }
    }
    .use_ebooks_text {
      @media @extraSmallDevices, @smallDevices {
        text-align: center;
      }
      .tree_emoji {
        font-size: 25px;
      }
    }
  }
}

#showEmptyCart {
  //display: none;
  .empty_cart {
    background: @white;
    min-height: 400px;
    border-radius: 10px;
    #emptyCartBox {
      width: 250px;
      height: 250px;
      margin: 0 auto;
      position: relative;
      top: 0;
      @media @extraSmallDevices, @smallDevices {
        width: 200px;
        height: 200px;
      }
    }
    h6 {
      line-height: normal;
    }
    .shopnow_btn {
      border: none;
      border-radius: 4px;
      box-shadow: 0 2px 4px @gray-light-shadow;
      font-weight: @medium;
      background-color: @orange;
      color: @white;
      width: auto;
      height: 35px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      font-size: 15px;
      margin-top: 7px;
    }
  }
}

.fixed-navbar {
  .shopping_cart {
    .cart_checkout {
      top: 125px;
      transition: all 0.3s ease;
    }
  }
}

footer, .footer-menus, .prepjoy_cta {
  @media @extraSmallDevices, @smallDevices {
    display: none;
  }
}

.shimmer_animated {
  background: #f6f7f8;
  background-image: linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
  background-repeat: no-repeat;
  background-size: 800px 150px;
  display: inline-block;
  position: relative;
  border-radius: 5px;
  -webkit-animation-duration: 1s;
  -webkit-animation-fill-mode: forwards;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-name: placeholderShimmer;
  -webkit-animation-timing-function: linear;
  * {
    opacity: 0;
  }
}

@keyframes shine {
  100% {
    left: 125%;
  }
}

@-webkit-keyframes placeholderShimmer {
  0% {
    background-position: -468px 0;
  }

  100% {
    background-position: 468px 0;
  }
}

// Arihant
.arihant,.whitelabel-site {
  .shopping_cart {
    margin-bottom: 50px;
  }
}

// Common overwriting Styles - or WhiteLabels
.radian_books, .oswaal_books {
  #showEmptyCart {
    .empty_cart {
      .shopnow_btn {
        background-color: @theme-primary-color;
      }
    }
  }
  .cart_checkout {
    .cart_buttons a.proceed_btn {
      background-color: @theme-primary-color;
    }
  }

}
.edugorilla {
  #showEmptyCart {
    .empty_cart {
      .shopnow_btn {
        background-color: #C9302C;
      }
    }
  }
  .cart_checkout {
    .cart_buttons a.proceed_btn {
      background-color: #C9302C;
    }
  }

}
.shippingAdressWrapper{
  background: #fff;
  border-radius: 5px;
  border: 1px solid rgba(0,0,0,0.2);
  padding: 1rem;
  h3{
    margin-bottom: 0 !important;
  }
}
.rechargeCartCover{
  width: 90px;
  height: 120px;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.printBookMobileCheckout{
  @media @extraSmallDevices,@smallDevices,@mediumDevices {
    padding-bottom: 0 !important;
  }
}
.printBookMobilePB{
  @media @extraSmallDevices,@smallDevices,@mediumDevices {
    padding-bottom: 150px!important;
  }
}

/* Coupon Code Section Styles */
.coupon_section {
  border-top: 1px solid #e9ecef;
  padding-top: 15px;
}

.coupon_section a {
  color: #007bff;
  font-weight: 500;
  font-size: 14px;
}

.coupon_section a:hover {
  color: #0056b3;
  text-decoration: none;
}

#couponSection {
  animation: slideDown 0.3s ease-in-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 100px;
  }
}

#couponCodeInput {
  border-radius: 5px 0 0 5px;
  border-right: none;
  font-size: 14px;
}

#applyCouponBtn {
  border-radius: 0 5px 5px 0;
  font-size: 14px;
  padding: 0.375rem 0.75rem;
}

#couponMessage {
  margin-top: 5px;
  font-size: 12px;
}

#couponMessage.text-success {
  color: #28a745 !important;
}

#couponMessage.text-danger {
  color: #dc3545 !important;
}

@media (max-width: 575.98px) {
  .coupon_section {
    padding: 10px 15px;
    margin: 0 -15px 15px -15px;
    background-color: #f8f9fa;
  }

  #couponCodeInput {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}
