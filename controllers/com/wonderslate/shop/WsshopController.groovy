package com.wonderslate.shop

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ExternalOrders
import com.wonderslate.data.KeyValueMst
import com.wonderslate.data.LevelsMst
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.publish.Blogs
import com.wonderslate.publish.BooksPermission
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.LevelSyllabus
import com.wonderslate.publish.SyllabusGradeDtl
import com.wonderslate.publish.SyllabusSubject
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.sql.Sql
import pl.touk.excel.export.WebXlsxExporter
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ValidityExtensionMst
import org.jsoup.Jsoup
import org.jsoup.nodes.Document

class WsshopController {

    WsshopService wsshopService
    UtilService utilService
    def springSecurityService
    def redisService
    DataProviderService dataProviderService
    DeliveryChargesService deliveryChargesService

    int accessCodeMinLimit = 1000
    def index() { }



    int getBookAccessCount(bookId)
    {
        def sql = "select count(*) codeCount from books_code_mst where book_id="+bookId

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)

        def count=0
        results.each { result ->
            count=count+result.codeCount
        }
        return count
    }


    @Transactional @Secured(['ROLE_USER'])
    def bookCdValidate() {
        Integer siteId = utilService.getSiteId(request,session)
      def json = ["status":wsshopService.bookCdValidate(params,springSecurityService.currentUser.username,siteId)]
        render json as JSON
    }

    @Transactional
    def getDefaultCategoryStructure(){
        Integer siteId = utilService.getSiteId(request,session)
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        String subject = null
        if("true".equals(params.subject)) subject="true"

        if("true".equals(siteMst.appInApp)) siteId = new Integer(1)

        if(redisService.("catalogStructure_"+siteId)==null||redisService.("catalogStructure_subject_"+siteId)==null) {
            String siteIdList = siteId.toString()


            if (siteId.intValue() == 1) {
                if (redisService.("siteIdList_" + siteId) == null) {
                    dataProviderService.getSiteIdList(siteId)
                }

                siteIdList = redisService.("siteIdList_" + siteId)
            }
            dataProviderService.getDefaultCategoryStructure(siteId,siteIdList,subject)
        }

        def json = ['defaultCategoryStructure':redisService.("catalogStructure_"+siteId)]
         if(subject!=null) json = ['defaultCategoryStructure':redisService.("catalogStructure_subject_"+siteId)]
        render json as JSON
    }

    @Transactional
    def getRelatedBooks(){
        def json
        String level=params.level,syllabus=params.syllabus,grade=params.grade,subject=params.subject,publisherId=null
        boolean freeBooks=false
        String eBooksUrl="";

        Integer siteId = utilService.getSiteId(request,session)
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if("true".equals(siteMst.appInApp)) siteId = new Integer(1)
        //1.getBookTagDtl from bookId
        //2. If school go till grade
        //3. If Competitive exams go till syllabus
        //4. If College go till syllabus.
        //If publisher thing is true then, add publisher condition.
        //If not enough books are available, keep going up.
        //put the limit logic to keyname.
        //if book id not present, then get latest books

        String siteIdList=siteId.toString()
        String keyName = "related_";
        if("true".equals(params.bestSellers))
        {
            keyName = "bestSellers"
        }

        if(siteId.intValue()==1){
            if(redisService.("siteIdList_"+siteId)==null) {
                dataProviderService.getSiteIdList(siteId)
            }

            siteIdList = redisService.("siteIdList_"+siteId)
        }
        if ("true".equals(params.freeBooks)) {
            freeBooks = true
        }

        if(params.bookId!=null&&!"null".equals(params.bookId)&&!"true".equals(params.bestSellers)) {
            BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(new Long(params.bookId))
            if(booksTagDtl!=null) {
                level = booksTagDtl.level
                syllabus = booksTagDtl.syllabus
                if ("School".equals(booksTagDtl.level)) {
                    grade = booksTagDtl.grade
                }


            }

        }

        if (level != null && !"null".equals(level)) {
            keyName = keyName + level.replaceAll("\\s+", "")
        }

        // currently pick best sellers only at the top level. As we get more books and sales we can enable syllabus and grade level also.
        if(!"true".equals(params.bestSellers)) {
            if (syllabus != null && !"null".equals(syllabus)) {
                keyName = keyName + "_" + syllabus.replaceAll("\\s+", "")
            }

            if (grade != null && !"null".equals(grade)) {
                keyName = keyName + "_" + grade.replaceAll("\\s+", "")
            }
        }

        if("true".equals(params.bestSellers)) eBooksUrl = "level=" + level
        else eBooksUrl = "level=" + level + "&syllabus=" + syllabus + "&grade=" + grade

        if(session.getAttribute("publisherLogo")!=null && session.getAttribute("publisherLogo")!=""){
            keyName = keyName + "_" + session.getAttribute("publisherLogo")
            publisherId = ""+session.getAttribute("publisherLogo")
        }

        if (redisService.("booksList_" + siteId + "_" + keyName.replaceAll("\\s+", "") + "_" + freeBooks) == null) {
            if(params.fromApp==null&&!"".equals(params.fromApp)) params.put("fromApp","true")
            dataProviderService.getNewBooksList(siteIdList, siteId,level, syllabus, grade,subject, keyName, freeBooks, publisherId,-1,null,null,params.fromApp,params.noOfBooks)
        }
        def books = redisService.("booksList_" + siteId + "_" + keyName.replaceAll("\\s+", "")+"_"+freeBooks)

        json = [
                'books':books,
                'eBooksUrl':eBooksUrl,
                'status' : books ? "OK" : "Nothing present"
        ]

        render json as JSON
    }

   @Transactional
    def addExternalOrder(){
        Integer siteId = utilService.getSiteId(request,session)
        def orderAckNo
        orderAckNo = wsshopService.addExternalOrder(params.orderFrom,params.orderId,params.itemCode,params.poValue,"systemCreated",siteId)

        def json = ["orderAckNo": orderAckNo,
                    orderId     : params.orderId,
                    'url'       : "https://www.wonderslate.com/amazonOrder?" + params.orderId]
        render json as JSON
    }

    @Transactional
    def updateExternalOrder(){
        String updateStatus = "Wrong order acknowledgement number"
        if(params.orderAckNo!=null) {
            Integer orderAckNo = new Integer(params.orderAckNo)

            ExternalOrders externalOrders = ExternalOrders.findById(orderAckNo)
            if (externalOrders != null && params.status!=null) {
                List externalOrdersList = ExternalOrders.findAllByOrderId(externalOrders.orderId)
                externalOrdersList.each{externalOrder ->
                    externalOrder.status = params.status
                    externalOrder.save(flush: true, failOnError: true)
                    updateStatus = "Order updated"
                }
            }
        }

        def json = ["status":updateStatus]
        render json as JSON
    }

    @Transactional
    def externalOrder(){
        //this method need to change after we add live Amazon / Third party real time order
        String status = "OrderNotFound"
        String bookId = null

        //step1. see if the order exists

        ExternalOrders externalOrders = ExternalOrders.findByOrderId(params.orderId)
        if(externalOrders!=null&&!"cancelled".equals(externalOrders.status)){
            if("bookAdded".equals(externalOrders.status)){
                status = "OrderCompleted"
            }else{
                //create order and add the book to library.
                if(springSecurityService.currentUser!=null){
                    //call the po creation and book adding service.
                    //add book Id
                    bookId = wsshopService.externalOrder(externalOrders,springSecurityService.currentUser.username)
                    status = "bookAdded"

                }
                else{
                    status = "UserRegister"
                    session.setAttribute("externalOrder",externalOrders)
                }
            }
        }
        def json = [status:status, bookId:bookId]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_CUSTOMER_SUPPORT'])
    def externalOrderCancelled(){
        ExternalOrders externalOrders = ExternalOrders.findByOrderId(params.orderId)
        String status ="NotFound"
        if(externalOrders!=null){
           wsshopService.externalOrderCancelled(externalOrders)
            status = "cancelled"
        }
        def json = [status : status]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_CUSTOMER_SUPPORT'])
    def orderStatus(){
        String status = "NotFound"
        String username = ""
        ExternalOrders externalOrders = ExternalOrders.findByOrderId(params.orderId)
        if(externalOrders!=null){
            status = externalOrders.status
            if("bookAdded".equals(status)){
                BooksPermission booksPermission = BooksPermission.findByClientPo(externalOrders.orderId)
                if(booksPermission!=null) username = booksPermission.username
            }
        }
        def json = [status : status, username: username]
        render json as JSON
    }

    @Transactional
    def getPublishedPublishersList(){
        Integer siteId = utilService.getSiteId(request,session)
        String siteIdList=siteId.toString()

        if(siteId.intValue()==1){
            if(redisService.("siteIdList_"+siteId)==null) {
                dataProviderService.getSiteIdList(siteId)
            }

            siteIdList = redisService.("siteIdList_"+siteId)
        }
        if (redisService.("publishedPublishers_" + siteId) == null) {
            dataProviderService.getPublishedPublishersList(siteIdList,siteId)
        }

        def json = [publishers : redisService.("publishedPublishers_" + siteId) ]
        render json as JSON

    }

    @Transactional
    def activeCategories(){
        Integer siteId = utilService.getSiteId(request,session)
        if (redisService.("activeCategories_" + siteId) == null) {
            wsshopService.activeCategories(siteId)
        }

        def json = [activeCategories : redisService.("activeCategories_" + siteId) ]
        render json as JSON
    }

    @Transactional
    def getActiveCategoriesAndSyllabus(){
        Integer siteId = utilService.getSiteId(request,session)
        if (redisService.("activeCategoriesSyllabus_" + siteId) == null) {
            wsshopService.getActiveCategoriesAndSyllabus(siteId)
        }

        def json = [activeCategoriesSyllabus_ : redisService.("activeCategoriesSyllabus_" + siteId) ]
        render json as JSON
    }

    @Transactional
    def migrateCategories(){
     List sgd = SyllabusGradeDtl.findAllBySyllabusAndSiteId("Engineering Entrance Exams",new Integer(1))
        sgd.each{ sg->
            LevelSyllabus levelSyllabus = new LevelSyllabus(level: "Engineering Entrances",syllabus: sg.grade,gradeType: "Seperate",siteId: sg.siteId)
           levelSyllabus.save(failOnError: true, flush: true)
            SyllabusGradeDtl syllabusGradeDtl = new SyllabusGradeDtl(syllabus:levelSyllabus.syllabus,grade:"All Exams",
                    createdBy: springSecurityService.currentUser.username, sort:new Integer(0) )
           syllabusGradeDtl.save(failOnError: true, flush: true)
        }

        //add subject to each new syllabus
        List ss = SyllabusSubject.findAllBySyllabus("Engineering Entrance Exams");

        //list of all syllabus of new level
        List ls = LevelSyllabus.findAllByLevelAndSiteId("Engineering Entrances",new Integer(1))
        ss.each { s->
            //insert syllabus subject
            ls.each { level ->
                SyllabusSubject syllabusSubject = new SyllabusSubject(syllabus: level.syllabus,subject: s.subject)
                syllabusSubject.save(failOnError: true, flush: true)
            }

        }

        // update bookTagDtl
        List btg = BooksTagDtl.findAllByLevelAndSyllabusAnd("Competitive Exams","Engineering Entrance Exams")


        btg.each{ tag ->

            BooksTagDtl booksTagDtl = new BooksTagDtl(bookId: tag.bookId, level: "Engineering Entrances",syllabus: tag.grade,grade: "All Exams",subject: tag.subject)
            booksTagDtl.save(failOnError: true, flush: true)
            BooksTagDtl booksTagDtl1 = new BooksTagDtl(bookId: tag.bookId, level: "Engineering Entrances",syllabus: tag.grade,grade: "All Exams",subject: tag.subject)
           booksTagDtl1.wsshop.save(failOnError: true, flush: true)
            tag.delete(flush: true)
        }

         sgd = SyllabusGradeDtl.findAllBySyllabusAndSiteId("Medical Entrance Exams",new Integer(1))
        sgd.each{ sg->
            LevelSyllabus levelSyllabus = new LevelSyllabus(level: "Medical Entrances",syllabus: sg.grade,gradeType: "Seperate",siteId: sg.siteId)
            levelSyllabus.save(failOnError: true, flush: true)
            SyllabusGradeDtl syllabusGradeDtl = new SyllabusGradeDtl(syllabus:levelSyllabus.syllabus,grade:"All Exams",
                    createdBy: springSecurityService.currentUser.username, sort:new Integer(0) )
            syllabusGradeDtl.save(failOnError: true, flush: true)
        }

        //add subject to each new syllabus
         ss = SyllabusSubject.findAllBySyllabus("Medical Entrance Exams");

        //list of all syllabus of new level
         ls = LevelSyllabus.findAllByLevelAndSiteId("Medical Entrances",new Integer(1))
        ss.each { s->
            //insert syllabus subject
            ls.each { level ->
                SyllabusSubject syllabusSubject = new SyllabusSubject(syllabus: level.syllabus,subject: s.subject)
                syllabusSubject.save(failOnError: true, flush: true)
            }

        }

        // update bookTagDtl
         btg = BooksTagDtl.findAllByLevelAndSyllabusAnd("Competitive Exams","Medical Entrance Exams")


        btg.each{ tag ->

            BooksTagDtl booksTagDtl = new BooksTagDtl(bookId: tag.bookId, level: "Medical Entrances",syllabus: tag.grade,grade: "All Exams",subject: tag.subject)
            booksTagDtl.save(failOnError: true, flush: true)
            BooksTagDtl booksTagDtl1 = new BooksTagDtl(bookId: tag.bookId, level: "Medical Entrances",syllabus: tag.grade,grade: "All Exams",subject: tag.subject)
            booksTagDtl1.wsshop.save(failOnError: true, flush: true)
            tag.delete(flush: true)
        }

        render "happy"
    }


    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def downloadBookAccessCode(){
        String bookCode=""
        Integer siteId = utilService.getSiteId(request,session)
        def results=wsshopService.mapBookAccessCode(new Integer(params.bookId),siteId,Integer.parseInt(params.numberOfCodes),params.campaignName)
        if (results != null && results.size() > 0) {
            List data = results.collect { codes ->
                bookCode=codes.code
                return [code: bookCode.length()>=13?bookCode:codes.code+params.bookId]
            }
            BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
            List headers = ["Code"]
            List withProperties = ["code"]
            def fileName = booksMst.title+"-BookAccessCode-"+(new Random()).nextInt(9999999)+".xlsx"
            new WebXlsxExporter().with {
                setResponseHeaders(response,fileName)
                fillHeader(headers)
                add(data, withProperties)
                save(response.outputStream)
            }
        }
        else{
            def json = ["status" :"failed","message":"No Tokens present, please contact Wonderslate."]
            render json as JSON
        }
    }


    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def checkAccessCodesExists(){
        String sql='select count(*) from books_code_mst where code is not null and book_id is null'
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        def json
        if(Integer.parseInt(params.numberOfCodes)<=results.get(0).values()[0])
            {
                json = ["status":"Ok"]
                render json as JSON
            }
        else{
            json = ["status" :"failed"]
            render json as JSON
        }
    }

    @Transactional
    def generateAccessCodes(){
        def limit=Integer.parseInt(params.limit);
        def siteId=Integer.parseInt(params.siteId);
        String sql='select count(*) from books_code_mst where code is not null and book_id is null and site_id='+siteId
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        def diff=limit-results.get(0).values()[0]
        wsshopService.createBookAccessCode(siteId,diff)
    }


    @Transactional  @Secured(['ROLE_BOOK_CREATOR'])
    def validityExtensionAdmin(){
        Integer bookId = new Integer(params.bookId)
        BooksMst booksMst = dataProviderService.getBooksMst(bookId)
        [bookId:bookId, title:booksMst.title, price: booksMst.price, validityDays: booksMst.validityDays]
    }

    @Transactional  @Secured(['ROLE_USER'])
    def getValidityExtensionDtls(){
        Integer bookId = new Integer(params.bookId)
        if(redisService.("validityExtensionDtls_"+bookId)==null) dataProviderService.getValidityExtensionDtls(bookId)
        def json = [priceDetails : redisService.("validityExtensionDtls_"+bookId)]
        render json as JSON

    }

    @Transactional  @Secured(['ROLE_BOOK_CREATOR'])
    def addValidityDetails(){
        Integer bookId =  new Integer(params.bookId)
        String username = springSecurityService.currentUser.username
        Integer validityMonths = new Integer(params.validityMonths)
        Double price = new Double(params.price)
        ValidityExtensionMst validityExtensionMst = new ValidityExtensionMst(bookId:bookId, username: username, validityMonths: validityMonths,price:price)
        validityExtensionMst.save(flush: true, failOnError: true)
        dataProviderService.getValidityExtensionDtls(bookId)
        def json = [priceDetails : redisService.("validityExtensionDtls_"+bookId)]
        render json as JSON
    }

    @Transactional  @Secured(['ROLE_BOOK_CREATOR'])
    def deleteValidityDetails(){
        Integer priceId = new Integer(params.priceId)
        ValidityExtensionMst validityExtensionMst = ValidityExtensionMst.findById(priceId)
        Integer bookId = validityExtensionMst.bookId
        validityExtensionMst.delete(flush: true)
        dataProviderService.getValidityExtensionDtls(bookId)
        def json = [priceDetails : redisService.("validityExtensionDtls_"+bookId)]
        render json as JSON

    }

    @Transactional
    def getBookDetailsByIsbn(){
        def json = wsshopService.getBookDetailsByIsbn(params)
        render json as JSON
        }

    @Transactional
    def addBookToCart(){
        Integer siteId = utilService.getSiteId(request,session)
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        String username
        if(springSecurityService.currentUser!=null)  username=springSecurityService.currentUser.username
        else username =""+siteId+"_"+session.getId()+"_temp"
        boolean excludeCheck = false
        if (siteMst != null && (siteMst.id.intValue() == 1 || siteMst.prepjoySite == "true")){
            String url = request.getRequestURL()
            if(url.indexOf("http://localhost")>-1||url.indexOf("wonderslate.com")>-1||url.indexOf("prepjoy.com")>-1) {
                excludeCheck = true
            }
        }
        BooksMst booksMst = dataProviderService.getBooksMst(new Integer(params.bookId))
        if("recharge".equals(booksMst.bookType)) excludeCheck=true
        if (excludeCheck || siteMst.id.intValue() == booksMst.siteId.intValue()|| (siteMst.associatedSites!=null&&siteMst.associatedSites.indexOf(""+booksMst.siteId)>-1)) {
        def json = wsshopService.addBookToCart(params,siteId,username)
        updateSessionCartCount(siteId)
        render json as JSON
        }else{
            def json = [status:"Book already purchased"]
            render json as JSON
        }
    }

    @Transactional
    def deleteBookFromCart(){

        Integer siteId = utilService.getSiteId(request,session)
        String username
        if(springSecurityService.currentUser!=null)  username=springSecurityService.currentUser.username
        else username =""+siteId+"_"+session.getId()+"_temp"
        def json = wsshopService.deleteBookFromCart(params,siteId,username)
        updateSessionCartCount(siteId)
        render json as JSON
    }

    @Transactional
    def updateSessionCartCount(siteId){
        String username
        if(springSecurityService.currentUser!=null)  username=springSecurityService.currentUser.username
        else username =""+siteId+"_"+session.getId()+"_temp"
        if(redisService.("usersCartBooksDetails_"+username)==null){
            dataProviderService.usersCartBooksDetailsByUsername(username,siteId)
        }
        session["userCartCount"]=Integer.parseInt(redisService.("usersCartBooksDetailsCount_"+username))
    }
    @Transactional
    def getUsersCartDetails(){
        def json
        String username

        Integer siteId = utilService.getSiteId(request,session)
        if(springSecurityService.currentUser!=null)  username=springSecurityService.currentUser.username
        else username =""+siteId+"_"+session.getId()+"_temp"


        List booklist1 = wsshopService.usersCartBooksDetails(username,siteId)
        if(booklist1 == null) {
            wsshopService.usersCartBooksDetailsByUsername(username,siteId)
            booklist1 = wsshopService.usersCartBooksDetails(username,siteId)
        }
        if(booklist1!=null) {
            for (int i = 0; i < booklist1.size(); i++) {
                def bookTemp = booklist1.get(i)
                String bookType = bookTemp.bookType ?: "eBook"
                HashMap<String, Double> discounts = wsshopService.getAutoDiscountForUser(bookTemp.bookId, siteId, bookType)
                bookTemp.discountValue = discounts.get('discountValue')
                bookTemp.discountId = discounts.get('discountId')
                bookTemp.couponCode=discounts.get('couponCode')
                booklist1.set(i, bookTemp)
            }
            json = [userCartDetails:booklist1,count:Integer.parseInt(redisService.("usersCartBooksDetailsCount_"+username))]
        }else{
            json =  [userCartDetails:[],count:0]
        }
        render json as JSON
    }

    @Transactional @Secured(['ROLE_USER'])
    def getUsersCartCount(){
        Integer siteId = utilService.getSiteId(request,session)
        def json = wsshopService.getUsersCartCount(springSecurityService.currentUser.username,siteId)
        render json as JSON
    }

    @Transactional @Secured(['ROLE_USER'])
    def addCartBooksToBuy(){
        def jsonObj = request.JSON
        Integer siteId = utilService.getSiteId(request,session)
        def json = wsshopService.addCartBooksToBuy(jsonObj,siteId)
        render json as JSON
    }

    @Transactional
    def getFeaturedPublishersAndCategories(){
        def json

        Integer siteId = utilService.getSiteId(request,session)

        int pageNo=0

        HashMap categoriesAndPublishers = wsshopService.getFeaturedPublishersAndCategories(siteId)
        json = [

                'featuredPublishers':categoriesAndPublishers.get("featuredPublishers"),
                'featuredCategories':categoriesAndPublishers.get("featuredCategories")
        ]

        render json as JSON
    }

    @Transactional
    def manageShopSpecials(){

        Integer siteId = session["siteId"]

        List keyValuesList  = KeyValueMst.findAllBySiteId(siteId)

        HashMap keyValues = new HashMap()

        keyValuesList.each{ keyValue ->
            keyValues.put(keyValue.keyName, keyValue.keyValue)
        }

        [keyValues : keyValues]
    }

    @Transactional
    def updateShopSpecials(){

        Integer siteId = session["siteId"]

        KeyValueMst keyValueMst = KeyValueMst.findByKeyNameAndSiteId(params.keyName,siteId)
        if(keyValueMst==null){
            //insert
            keyValueMst = new KeyValueMst(keyName: params.keyName, keyValue: params.keyValue,siteId:siteId)
        }
        else{
            //update
            keyValueMst.keyValue = params.keyValue
        }
        keyValueMst.save(flush: true, failOnError: true)
        wsshopService.storeAdditionalInfo(siteId)
        def json = ['status':'updated']
        render json as JSON

    }

    @Transactional
    def cart() {
        String username
        Integer siteId = session["siteId"]
        if(springSecurityService.currentUser!=null)  username=springSecurityService.currentUser.username
        else username =""+siteId+"_"+session.getId()+"_temp"

        BillShipAddressHolder billShipAddressHolder = BillShipAddressHolder.findByUsername(username)
        [title:'Cart',commonTemplate:"true",billShip:billShipAddressHolder]
    }

    @Transactional @Secured(['ROLE_CLIENT_ORDER_MANAGER'])

    def orderManagement(){
        KeyValueMst keyValueMst = KeyValueMst.findByKeyName("secretKey_"+session["siteId"])

        [secretKey:keyValueMst.keyValue, commonTemplate: "true"]
    }

    @Transactional  @Secured(['ROLE_USER'])
    def getPackageBooksDetail(){
        Integer bookId = new Integer(params.bookId)
        if(redisService.("packageBooksDetail_"+bookId)==null) dataProviderService.getPackageBooksDetail(bookId)
        def json = [books : redisService.("packageBooksDetail_"+bookId)]
        render json as JSON

    }

    @Transactional @Secured(['ROLE_USER'])
    def moveCartItems()
    {
        String oldUser = ""+session["siteId"]+"_"+session.getId()+"_temp"
        String newUser = springSecurityService.currentUser.username
        Integer siteId = utilService.getSiteId(request,session)
        wsshopService.moveCartItems(oldUser,newUser,siteId)
         def json = ["status":"ok"]
        render json as JSON
    }

    @Transactional
    def addBillShipAddressToHolder() {
        Integer siteId = utilService.getSiteId(request,session)
        String username
        if(springSecurityService.currentUser!=null)  username=springSecurityService.currentUser.username
        else username =""+siteId+"_"+session.getId()+"_temp"
        def billShipAddressHolder = wsshopService.addBillShipAddressToHolder(params,username,siteId)
        Double shippingCharges = deliveryChargesService.deliveryChargesCalculator(username,false,null)
        def json = ["status":"ok",shippingCharges:shippingCharges, addressHolderId:billShipAddressHolder.id]
        render json as JSON
    }

    @Transactional
    def getStateAndCity(){
        def apiURL = "https://api.postalpincode.in/pincode/${params.pincode}"
        def json
        try {
            def response = new URL(apiURL).getText()
            def result = JSON.parse(response)


            if (result && result.size() > 0 && result[0].Status == "Success") {
                def postOffice = result[0].PostOffice[0]
                def state = postOffice.State
                def city = postOffice.District

                // Do something with the retrieved state and city
                // For example, you can return them as JSON response
                json = [status:"OK",state: state, city: city]
            } else {
                // Handle error scenario when pincode is not found
               json = [status: "404", text: "Pincode not found"]
            }
        } catch (Exception e) {
            // Handle any exception occurred during API call
            json =[status: "500", text: "Error occurred while fetching data from API"]
        }
        render json as JSON
    }



    @Transactional
    def updateGradeDetails(){

        Blogs blogs
        if(params.fullForm!=null&&!"".equals(params.fullForm)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "fullForm")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "fullForm", colValue: params.fullForm)
            else blogs.colValue = params.fullForm
            blogs.save(flush: true, failOnError: true)
        }
        if(params.introduction!=null&&!"".equals(params.introduction)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "introduction")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "introduction", colValue: params.introduction)
            else blogs.colValue = params.introduction
            blogs.save(flush: true, failOnError: true)
        }
        if(params.eligibility!=null&&!"".equals(params.eligibility)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "eligibility")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "eligibility", colValue: params.eligibility)
            else blogs.colValue = params.eligibility
            blogs.save(flush: true, failOnError: true)
        }
        if(params.examPattern!=null&&!"".equals(params.examPattern)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "examPattern")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "examPattern", colValue: params.examPattern)
            else blogs.colValue = params.examPattern
            blogs.save(flush: true, failOnError: true)
        }
        if(params.fullSyllabus!=null&&!"".equals(params.fullSyllabus)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "fullSyllabus")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "fullSyllabus", colValue: params.fullSyllabus)
            else blogs.colValue = params.fullSyllabus
            blogs.save(flush: true, failOnError: true)
        }
        if(params.applicationProcess!=null&&!"".equals(params.applicationProcess)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "applicationProcess")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "applicationProcess", colValue: params.applicationProcess)
            else blogs.colValue = params.applicationProcess
            blogs.save(flush: true, failOnError: true)
        }
        if(params.examDates!=null&&!"".equals(params.examDates)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "examDates")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "examDates", colValue: params.examDates)
            else blogs.colValue = params.examDates
            blogs.save(flush: true, failOnError: true)
        }
        if(params.faq!=null&&!"".equals(params.faq)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "faq")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "faq", colValue: params.faq)
            else blogs.colValue = params.faq
            blogs.save(flush: true, failOnError: true)
        }
        if(params.pqpLinks!=null&&!"".equals(params.pqpLinks)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "pqpLinks")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "pqpLinks", colValue: params.pqpLinks)
            else blogs.colValue = params.pqpLinks
            blogs.save(flush: true, failOnError: true)
        }
        if(params.generalLinks!=null&&!"".equals(params.generalLinks)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "generalLinks")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "generalLinks", colValue: params.generalLinks)
            else blogs.colValue = params.generalLinks
            blogs.save(flush: true, failOnError: true)
        }
        if(params.generalInfo!=null&&!"".equals(params.generalInfo)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "generalInfo")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "generalInfo", colValue: params.generalInfo)
            else blogs.colValue = params.generalInfo
            blogs.save(flush: true, failOnError: true)
        }
        if(params.fullFormHindi!=null&&!"".equals(params.fullFormHindi)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "fullFormHindi")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "fullFormHindi", colValue: params.fullFormHindi)
            else blogs.colValue = params.fullFormHindi
            blogs.save(flush: true, failOnError: true)
        }
        if(params.introductionHindi!=null&&!"".equals(params.introductionHindi)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "introductionHindi")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "introductionHindi", colValue: params.introductionHindi)
            else blogs.colValue = params.introductionHindi
            blogs.save(flush: true, failOnError: true)
        }
        if(params.eligibilityHindi!=null&&!"".equals(params.eligibilityHindi)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "eligibilityHindi")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "eligibilityHindi", colValue: params.eligibilityHindi)
            else blogs.colValue = params.eligibilityHindi
            blogs.save(flush: true, failOnError: true)
        }
        if(params.examPatternHindi!=null&&!"".equals(params.examPatternHindi)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "examPatternHindi")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "examPatternHindi", colValue: params.examPatternHindi)
            else blogs.colValue = params.examPatternHindi
            blogs.save(flush: true, failOnError: true)
        }
        if(params.fullSyllabusHindi!=null&&!"".equals(params.fullSyllabusHindi)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "fullSyllabusHindi")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "fullSyllabusHindi", colValue: params.fullSyllabusHindi)
            else blogs.colValue = params.fullSyllabusHindi
            blogs.save(flush: true, failOnError: true)
        }
        if(params.applicationProcessHindi!=null&&!"".equals(params.applicationProcessHindi)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "applicationProcessHindi")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "applicationProcessHindi", colValue: params.applicationProcessHindi)
            else blogs.colValue = params.applicationProcessHindi
            blogs.save(flush: true, failOnError: true)
        }
        if(params.examDatesHindi!=null&&!"".equals(params.examDatesHindi)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "examDatesHindi")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "examDatesHindi", colValue: params.examDatesHindi)
            else blogs.colValue = params.examDatesHindi
            blogs.save(flush: true, failOnError: true)
        }
        if(params.faqHindi!=null&&!"".equals(params.faqHindi)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "faqHindi")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "faqHindi", colValue: params.faqHindi)
            else blogs.colValue = params.faqHindi
            blogs.save(flush: true, failOnError: true)
        }
        if(params.pqpLinksHindi!=null&&!"".equals(params.pqpLinksHindi)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "pqpLinksHindi")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "pqpLinksHindi", colValue: params.pqpLinksHindi)
            else blogs.colValue = params.pqpLinksHindi
            blogs.save(flush: true, failOnError: true)
        }
        if(params.generalLinksHindi!=null&&!"".equals(params.generalLinksHindi)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "generalLinksHindi")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "generalLinksHindi", colValue: params.generalLinksHindi)
            else blogs.colValue = params.generalLinksHindi
            blogs.save(flush: true, failOnError: true)
        }

        if(params.youtubeChannels!=null&&!"".equals(params.youtubeChannels)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "youtubeChannels")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "youtubeChannels", colValue: params.youtubeChannels)
            else blogs.colValue = params.youtubeChannels
            blogs.save(flush: true, failOnError: true)
        }

        if(params.bestBooks!=null&&!"".equals(params.bestBooks)) {
            blogs = Blogs.findBySyllabusAndGradeAndSubjectAndSiteIdAndColName(params.syllabus, params.grade,params.subject, new Integer(1), "bestBooks")
            if (blogs == null) blogs = new Blogs(syllabus: params.syllabus, grade: params.grade,subject: params.subject, siteId: new Integer(1), colName: "bestBooks", colValue: params.bestBooks)
            else blogs.colValue = params.bestBooks
            blogs.save(flush: true, failOnError: true)
        }


        def json = [status:"Updated"]
        render json as JSON
    }

   @Transactional
    def addGradeInfo(){
       [levelsMstList: LevelsMst.findAllBySiteId(new Integer(1))]
   }

    @Transactional
    def getSyllabusDtl(){
        LevelSyllabus levelSyllabus = LevelSyllabus.findBySyllabusAndSiteId(params.syllabus,new Integer(1))

        HashMap info  = new HashMap()
        info.id=levelSyllabus.id
        info.syllabus = params.syllabus
        List blogs = Blogs.findAllBySiteIdAndSyllabusAndGrade(new Integer(1),params.syllabus,"null")
        blogs.each{blog->
            info.put(blog.colName,blog.colValue)
        }
        def json = [info:info,dataSource:'syllabus']
        render json as JSON
    }
    @Transactional
    def getGradeDtl(){
        SyllabusGradeDtl syllabusGradeDtl = SyllabusGradeDtl.findBySyllabusAndGradeAndSiteId(params.syllabus,params.grade,new Integer(1))

        if(syllabusGradeDtl==null&&params.grade.indexOf("Semester")>-1){
            String grade = (""+params.grade).replace("Semester","").trim()
            syllabusGradeDtl = SyllabusGradeDtl.findBySyllabusAndGradeAndSiteId(params.syllabus,grade,new Integer(1))
        }
        HashMap info  = new HashMap()

        info.id=syllabusGradeDtl.id

        info.syllabus = params.syllabus
        info.grade= params.grade
        List blogs = Blogs.findAllBySiteIdAndSyllabusAndGrade(new Integer(1),params.syllabus,params.grade)
        blogs.each{blog->
            info.put(blog.colName,blog.colValue)
        }
        def json = [info:info,dataSource: 'grade']
        render json as JSON
    }

    @Transactional
    def getSubjectDtl(){
        SyllabusSubject syllabusSubject = SyllabusSubject.findBySyllabusAndSubject(params.syllabus,params.subject)
        HashMap info  = new HashMap()

        info.id=syllabusSubject.id

        info.syllabus = params.syllabus
        info.grade= params.grade
        info.subject = params.subject
        List blogs = Blogs.findAllBySiteIdAndSyllabusAndGradeAndSubject(new Integer(1),params.syllabus,params.grade,params.subject)
        blogs.each{blog->
            info.put(blog.colName,blog.colValue)
        }
        def json = [info:info,dataSource: 'subject']
        render json as JSON
    }

    @Transactional
    def blogEnglish(){
    try {
        String pageTitle = "Best books for "
        String topLevelTitle = "";
        String fullTitle = ""
        String seoDesc
        HashMap info = new HashMap()

        if ("syllabus".equals(params.dataSource)) {
            LevelSyllabus levelSyllabus = LevelSyllabus.findById(new Integer(params.syllabusId))
            println("syllabus is " + levelSyllabus.syllabus)
            List blogs = Blogs.findAllBySiteIdAndSyllabusAndGrade(new Integer(1), levelSyllabus.syllabus, "null")
            println("number of rows=" + blogs.size())
            blogs.each { blog ->
                println("${blog.colName}=${blog.colValue}")
                info.put(blog.colName, blog.colValue)
            }
            pageTitle += levelSyllabus.syllabus
            topLevelTitle = levelSyllabus.syllabus
            fullTitle = levelSyllabus.syllabus
            seoDesc = info.introduction
            params.put("syllabus", levelSyllabus.syllabus)
        } else if ("grade".equals(params.dataSource)) {
            SyllabusGradeDtl syllabusGradeDtl = SyllabusGradeDtl.findById(new Integer(params.gradeId))
            LevelSyllabus levelSyllabus = LevelSyllabus.findBySyllabusAndSiteId(syllabusGradeDtl.syllabus, new Integer(1))

            HashMap syllabusInfo = new HashMap()
            List syllabusBlogs = Blogs.findAllBySiteIdAndSyllabusAndGrade(new Integer(1), syllabusGradeDtl.syllabus, "null")
            syllabusBlogs.each { blog ->
                syllabusInfo.put(blog.colName, blog.colValue)
            }
            List blogs = Blogs.findAllBySiteIdAndSyllabusAndGrade(new Integer(1), syllabusGradeDtl.syllabus, syllabusGradeDtl.grade)
            blogs.each { blog ->
                info.put(blog.colName, blog.colValue)
            }
            params.put("syllabus", syllabusGradeDtl.syllabus)
            params.put("grade", syllabusGradeDtl.grade)
            seoDesc = info.introduction
            if ("School".equals(levelSyllabus.level)) {
                seoDesc = syllabusInfo.introduction
                info.fullForm = syllabusInfo.fullForm
                info.introduction = syllabusInfo.introduction
                pageTitle += levelSyllabus.syllabus + " Class " + syllabusGradeDtl.grade
                topLevelTitle = syllabusGradeDtl.syllabus
                fullTitle = syllabusGradeDtl.syllabus + " Class " + syllabusGradeDtl.grade

            } else if ("College".equals(levelSyllabus.syllabus)) {
                seoDesc = syllabusInfo.introduction
                info.fullForm = syllabusInfo.fullForm
                info.introduction = syllabusInfo.introduction
                pageTitle += syllabusGradeDtl.syllabus + " Semester " + syllabusGradeDtl.grade
                topLevelTitle = syllabusGradeDtl.syllabus
                fullTitle = syllabusGradeDtl.syllabus + " Class " + syllabusGradeDtl.grade
            } else {
                pageTitle += syllabusGradeDtl.grade
                topLevelTitle = syllabusGradeDtl.grade
                fullTitle = syllabusGradeDtl.grade
            }
        } else {
            SyllabusSubject syllabusSubject = SyllabusSubject.findById(new Integer(params.subjectId))

            HashMap syllabusInfo = new HashMap()
            List syllabusBlogs = Blogs.findAllBySiteIdAndSyllabusAndSubject(new Integer(1), syllabusSubject.syllabus, "null")
            syllabusBlogs.each { blog ->
                syllabusInfo.put(blog.colName, blog.colValue)
            }
            List blogs = Blogs.findAllBySiteIdAndSyllabusAndSubject(new Integer(1), syllabusSubject.syllabus, syllabusSubject.subject)
            blogs.each { blog ->
                info.put(blog.colName, blog.colValue)
            }
            params.put("syllabus", syllabusSubject.syllabus)
            params.put("subject", syllabusSubject.subject)
            seoDesc = info.introduction

            pageTitle += syllabusSubject.subject
            topLevelTitle = syllabusSubject.subject
            fullTitle = syllabusSubject.subject

        }
        HashMap booksAndPublishers = wsshopService.getBooksList(params, 1, 0)
        println("seoDesc=" + seoDesc)
        if (seoDesc != null) {
            Document document = Jsoup.parse(seoDesc as String);
            seoDesc = document.text();
        }
        String keywords = fullTitle + "," +
                "Best books for " + fullTitle + "," +
                topLevelTitle + " full form," +
                topLevelTitle + " eligibitity," +
                topLevelTitle + " exam pattern," +
                topLevelTitle + " full syllabus," +
                topLevelTitle + " application process," +
                topLevelTitle + " exam dates," +
                topLevelTitle + " frequently asked questions," +
                fullTitle + " previous year question papers," +
                "Best YouTube channels for " + fullTitle;
                session["videosBookList"]=booksAndPublishers

        [info: info, title: pageTitle, topLevelTitle: topLevelTitle, fullTitle: fullTitle, keywords: keywords, seoDesc: seoDesc, booksList: booksAndPublishers]
    }catch(Exception e){
        println(e.toString())
            render "Error in blog"
        }
    }

    @Transactional
    def blogHindi() {
        try {
            String pageTitle = ""
            String topLevelTitle = "";
            String fullTitle = ""
            String seoDesc
            HashMap info = new HashMap()
            if ("syllabus".equals(params.dataSource)) {
                LevelSyllabus levelSyllabus = LevelSyllabus.findById(new Integer(params.syllabusId))
                List blogs = Blogs.findAllBySiteIdAndSyllabusAndGrade(new Integer(1), levelSyllabus.syllabus, "null")
                blogs.each { blog ->
                    info.put(blog.colName, blog.colValue)
                }
                pageTitle += levelSyllabus.syllabus
                topLevelTitle = levelSyllabus.syllabus
                fullTitle = levelSyllabus.syllabus
                seoDesc = info.introductionHindi
                params.put("syllabus", levelSyllabus.syllabus)
            } else if ("grade".equals(params.dataSource)) {
                SyllabusGradeDtl syllabusGradeDtl = SyllabusGradeDtl.findById(new Integer(params.gradeId))
                LevelSyllabus levelSyllabus = LevelSyllabus.findBySyllabusAndSiteId(syllabusGradeDtl.syllabus, new Integer(1))

                HashMap syllabusInfo = new HashMap()
                List syllabusBlogs = Blogs.findAllBySiteIdAndSyllabusAndGrade(new Integer(1), syllabusGradeDtl.syllabus, "null")
                syllabusBlogs.each { blog ->
                    syllabusInfo.put(blog.colName, blog.colValue)
                }
                List blogs = Blogs.findAllBySiteIdAndSyllabusAndGrade(new Integer(1), syllabusGradeDtl.syllabus, syllabusGradeDtl.grade)
                blogs.each { blog ->
                    info.put(blog.colName, blog.colValue)
                }
                params.put("syllabus", syllabusGradeDtl.syllabus)
                params.put("grade", syllabusGradeDtl.grade)
                seoDesc = info.introduction
                if ("School".equals(levelSyllabus.level)) {
                    seoDesc = syllabusInfo.introduction
                    info.fullForm = syllabusInfo.fullForm
                    info.introductionHindi = syllabusInfo.introductionHindi
                    pageTitle += levelSyllabus.syllabus + " Class " + syllabusGradeDtl.grade
                    topLevelTitle = syllabusGradeDtl.syllabus
                    fullTitle = syllabusGradeDtl.syllabus + " Class " + syllabusGradeDtl.grade

                } else if ("College".equals(levelSyllabus.syllabus)) {
                    seoDesc = syllabusInfo.introduction
                    info.fullForm = syllabusInfo.fullForm
                    info.introductionHindi = syllabusInfo.introductionHindi
                    pageTitle += syllabusGradeDtl.syllabus + " Semester " + syllabusGradeDtl.grade
                    topLevelTitle = syllabusGradeDtl.syllabus
                    fullTitle = syllabusGradeDtl.syllabus + " Class " + syllabusGradeDtl.grade
                } else {
                    pageTitle += syllabusGradeDtl.grade
                    topLevelTitle = syllabusGradeDtl.grade
                    fullTitle = syllabusGradeDtl.grade
                }
            }else {
                SyllabusSubject syllabusSubject = SyllabusSubject.findById(new Integer(params.subjectId))

                HashMap syllabusInfo = new HashMap()
                List syllabusBlogs = Blogs.findAllBySiteIdAndSyllabusAndSubject(new Integer(1), syllabusSubject.syllabus, "null")
                syllabusBlogs.each { blog ->
                    syllabusInfo.put(blog.colName, blog.colValue)
                }
                List blogs = Blogs.findAllBySiteIdAndSyllabusAndSubject(new Integer(1), syllabusSubject.syllabus, syllabusSubject.subject)
                blogs.each { blog ->
                    info.put(blog.colName, blog.colValue)
                }
                params.put("syllabus", syllabusSubject.syllabus)
                params.put("subject", syllabusSubject.subject)
                seoDesc = info.introduction

                pageTitle += syllabusSubject.subject
                topLevelTitle = syllabusSubject.subject
                fullTitle = syllabusSubject.subject

            }
            pageTitle += " के लिए सर्वोत्तम पुस्तके"
            HashMap booksAndPublishers = wsshopService.getBooksList(params, 1, 0)
            Document document = Jsoup.parse(seoDesc as String);
            seoDesc = document.text();
            String keywords = fullTitle + "," +
                    pageTitle + "," +
                    topLevelTitle + " पूर्ण प्रपत्र," +
                    topLevelTitle + " पात्रता," +
                    topLevelTitle + " परीक्षा पैटर्न," +
                    topLevelTitle + " पूरा सिलेबस," +
                    topLevelTitle + " आवेदन प्रक्रिया," +
                    topLevelTitle + " परीक्षा तिथियाँ," +
                    topLevelTitle + " अक्सर पूछे जाने वाले प्रश्नों," +
                    topLevelTitle + " पिछले वर्षों के प्रश्न पत्र," +
                    topLevelTitle + " के लिए सर्वश्रेष्ठ यूट्यूब चैनल";

            [info: info, title: pageTitle, topLevelTitle: topLevelTitle, fullTitle: fullTitle, keywords: keywords, seoDesc: seoDesc, booksList: booksAndPublishers]
        }catch(Exception e){
            println(e.toString())
            render "Error in blog"
        }
    }

}
